#!/usr/bin/env node

"use strict";

/*
    https://github.com/visionmedia/commander.js
    http://visionmedia.github.io/commander.js/
    https://github.com/visionmedia/commander.js/tree/master/examples

    sudo npm install ./ -g
*/

var path = require('path')
var program = require('commander')
var pkg = require(path.resolve(__dirname, '../package.json'))
var Random = require('../dist/mock.js').Random

program
    .version(pkg.version)
    .on('--help', function() {
        console.log('  Examples:')
        console.log('')
        console.log('    $ random date yyyy-MM-dd')
        console.log('    $ random time HH:mm:ss')
        console.log('')
    })

;
(function() {

    var FN_ARGS = /^function\s*[^\(]*\(\s*([^\)]*)\)/m
    var FN_ARG_SPLIT = /,/
    var FN_ARG = /^\s*(_?)(\S+?)\1\s*$/
    var STRIP_COMMENTS = /((\/\/.*$)|(\/\*[\s\S]*?\*\/))/mg
    var EXCLUDE = [
        'extend',
        'dataImage', // mock/random/image
        'capitalize', 'upper', 'lower', 'pick', 'shuffle', 'order', // mock/random/helper.js
        'increment', 'inc' // mock/random/misc.js
    ]

    function parseArgs(fn) {
        var fnText = fn.toString().replace(STRIP_COMMENTS, '')
        var argDecl = fnText.match(FN_ARGS)
        return argDecl[1].split(FN_ARG_SPLIT).join(', ')
    }

    Object.keys(Random).forEach(function(key) {
        if (key[0] === '_') return
        if (EXCLUDE.indexOf(key) !== -1) return

        var fn = Random[key]
        if (typeof fn === 'function') {
            var argDecl = parseArgs(fn)
            if (argDecl) argDecl = '( ' + argDecl + ' )'
            else argDecl = '()';

            program
                .command(key)
                .description('Random.' + key + argDecl)
                .action(function() {
                    var args = [].slice.call(arguments, 0, -1)
                    var result = fn.apply(Random, args)
                    console.log(result)
                })
        }
    })

})()

program.parse(process.argv)

;
(function() {
    var cmd = program.args[0]
    if (!cmd) {
        process.stdout.write(program.helpInformation())
        program.emit('--help')
        process.exit()
    }
})()