export default {
  nav: {
    home: '首页',
    about: '关于我们',
    services: '服务',
    portfolio: '作品集',
    blog: '博客',
    contact: '联系我们',
    menu: '菜单'
  },
  common: {
    loading: '加载中...',
    error: '出错了',
    retry: '重试',
    close: '关闭',
    open: '打开',
    save: '保存',
    cancel: '取消',
    confirm: '确认',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    search: '搜索',
    filter: '筛选',
    sort: '排序',
    more: '更多',
    language: '语言'
  },
  language: {
    chinese: '中文',
    english: 'English',
    japanese: '日本語',
    korean: '한국어'
  },
  albums: {
    title: '专辑EP',
    carouselMode: '轮播模式',
    listMode: '列表模式',
    noData: '暂无专辑数据',
    noDataDescription: '专辑数据正在准备中，请稍后再来查看'
  },
  albumDetail: {
    backToAlbums: '所有专辑',
    language: '语言',
    recordCompany: '唱片公司',
    albumType: '唱片类型',
    trackList: '专辑曲目单',
    songIntroduction: '歌曲介绍'
  },
  artists: {
    artist:'艺人',
    songwriter:'词曲人',
    producer:'制作人',
    backToAllArtists: '所有艺人',
    backToAllsongwriter:'所有词曲人',
    backToAllproducer:'所有制作人',
    member:"组合成员",
    agency:"代理关系",
    musicAlbum:"歌曲&专辑",
  },
  footer: {
    slogan: '让每段创作轨迹都被世界听见',
    friendlyLinks: '友好链接：',
    copyright: '海之轨迹有限公司，保留所有权利',
    icp: '京ICP备19032793号',
    backToTop: '顶部',
    links: {
      haikuiMusic: '海葵音乐',
      haixingMusic: '海星音乐',
      haimianMusic: '海绵音乐'
    }
  },
  notFound: {
    title: '页面未找到',
    description: '抱歉，您访问的页面不存在或已被移除。',
    homeButton: '返回首页',
    backButton: '返回上页'
  }
}
