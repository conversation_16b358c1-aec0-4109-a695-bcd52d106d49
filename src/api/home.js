import request from './request'

// 首页相关 API

// 获取艺人 词曲人 制作人列表
export async function getPersonList(params) {
  return request.get('/personList', { params })
}
//搜索艺人，词曲人，制作人
export async function getPerson(params) {
  return request.get('/person', { params })
}
//获取艺人，词曲人，制作人详情
export async function getPersonDetail(params) {
  console.log("获取艺人，词曲人，制作人详情", params);
  return request.get('/detail', { params })
}

// 获取新闻列表
export async function getNewsList(params = {}) {
  return request.get('/news', { params })
}

// 获取首页 Banner
export async function getBanner() {
  return request.get('/banner')
}