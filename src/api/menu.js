// 模拟菜单数据接口
export const getMenuData = async () => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  return {
    success: true,
    data: {
      mainMenu: [
        {
          id: 'about',
          title: {
            zh: '关于我们',
            en: 'About Us',
            ja: '私たちについて',
            ko: '회사 소개'
          },
          path: '/about',
          hasSubMenu: false
        },
        {
          id: 'artists',
          title: {
            zh: '艺人',
            en: 'Artists',
            ja: 'アーティスト',
            ko: '아티스트'
          },
          path: '/artists/artist',
          hasSubMenu: true,
          subMenu: [
            {
              id: 'artist-1',
              name: '魏晗',
              path: '/detail/aritst/weihan',
              featured: true
            },
            {
              id: 'artist-2', 
              name: 'KIIRA<PERSON>',
              path: '/artists/kiiras'
            },
            {
              id: 'artist-3',
              name: '魏晗',
              path: '/artists/weihan-2'
            },
            {
              id: 'artist-4',
              name: 'KIIRA<PERSON>', 
              path: '/artists/kiiras-2'
            },
            {
              id: 'artist-5',
              name: '魏晗',
              path: '/artists/weihan-3'
            },
            {
              id: 'artist-6',
              name: 'KII<PERSON><PERSON>',
              path: '/artists/kiiras-3'
            }
          ]
        },
        {
          id: 'songwriters',
          title: {
            zh: '词曲人',
            en: 'Songwriters',
            ja: '作詞作曲家',
            ko: '작사작곡가'
          },
          path: '/artists/songwriter',
          hasSubMenu: true,
          subMenu: [
            {
              id: 'songwriter-1',
              name: '词曲人名字',
              path: '/detail/songwriter/songwriter-2'
            },
            {
              id: 'songwriter-2',
              name: '词曲人名字',
              path: '/detail/songwriter/songwriter-2'
            },
            {
              id: 'songwriter-3',
              name: '词曲人名字',
              path: '/songwriters/songwriter-3'
            },
            {
              id: 'songwriter-4',
              name: '词曲人名字',
              path: '/songwriters/songwriter-4'
            },
            {
              id: 'songwriter-5',
              name: '词曲人名字',
              path: '/songwriters/songwriter-5'
            },
            {
              id: 'songwriter-6',
              name: '词曲人名字',
              path: '/songwriters/songwriter-6'
            }
          ]
        },
        {
          id: 'producers',
          title: {
            zh: '制作人',
            en: 'Producers',
            ja: 'プロデューサー',
            ko: '프로듀서'
          },
          path: '/artists/producer',
          hasSubMenu: true,
          subMenu: [
            {
              id: 'producer-1',
              name: '制作人名字',
              path: '/detail/producer/producer-1'
            },
            {
              id: 'producer-2',
              name: '制作人名字',
              path: '/producers/producer-2'
            },
            {
              id: 'producer-3',
              name: '制作人名字',
              path: '/producers/producer-3'
            },
            {
              id: 'producer-4',
              name: '制作人名字',
              path: '/producers/producer-4'
            },
            {
              id: 'producer-5',
              name: '制作人名字',
              path: '/producers/producer-5'
            },
            {
              id: 'producer-6',
              name: '制作人名字',
              path: '/producers/producer-6'
            }
          ]
        },
        {
          id: 'albums',
          title: {
            zh: '专辑EP',
            en: 'Albums & EPs',
            ja: 'アルバム・EP',
            ko: '앨범 & EP'
          },
          path: '/albums',
          hasSubMenu: false
        },
        {
          id: 'services',
          title: {
            zh: '服务项目',
            en: 'Services',
            ja: 'サービス',
            ko: '서비스'
          },
          path: '/services',
          hasSubMenu: false
        },
        {
          id: 'news',
          title: {
            zh: '厂牌新闻',
            en: 'Label News',
            ja: 'レーベルニュース',
            ko: '레이블 뉴스'
          },
          path: '/news',
          hasSubMenu: false
        }
      ]
    }
  }
}

// 获取艺人详情
export const getArtistDetail = async (artistId) => {
  await new Promise(resolve => setTimeout(resolve, 200))
  
  return {
    success: true,
    data: {
      id: artistId,
      name: artistId.includes('weihan') ? '魏晗' : 'KIIRAS',
      bio: '艺人简介...',
      albums: [],
      songs: []
    }
  }
}
