@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-35-Thin.woff2") format("woff2");
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-45-Light.woff2") format("woff2");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-55-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-65-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-65-Medium.woff2") format("woff2");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-85-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter';
  src: url("/fonts/Inter-VariableFont.woff2") format("woff2-variations");
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
  font-named-instance: 'Regular';
}
@font-face {
  font-family: 'AvantGarde CE Regular';
  src: url("/fonts/AvantGarde CE Regular.woff2") format("woff2");
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
  font-named-instance: 'Regular';
}

