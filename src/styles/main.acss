@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-35-Thin.woff2") format("woff2");
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-45-Light.woff2") format("woff2");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-55-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-65-Medium.woff2") format("woff2");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-65-Medium.woff2") format("woff2");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url("/fonts/AlibabaPuHuiTi-2-85-Bold.woff2") format("woff2");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter';
  src: url("/fonts/Inter-VariableFont.woff2") format("woff2-variations");
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
  font-named-instance: 'Regular';
}

.container {
  max-width: 1320px;
  margin: 148px auto 180px;
  padding: 0 0;
}

@media (max-width: 1199px) {
  .container {
    margin: 100px auto 120px;
    padding: 0 60px;
  }
}

@media (max-width: 767px) {
  .container {
    margin: 100px auto 120px;
    padding: 0 20px;
  }
}

@media (max-width: 479px) {
  .container {
    margin: 50px auto 100px;
    padding: 0 16px;
  }
}

.container-fluid {
  width: 100%;
  padding: 0 60px;
}

@media (max-width: 1199px) {
  .container-fluid {
    padding: 0 60px;
  }
}

@media (max-width: 767px) {
  .container-fluid {
    padding: 0 20px;
  }
}

@media (max-width: 479px) {
  .container-fluid {
    padding: 0 16px;
  }
}

.container-narrow {
  max-width: 960px;
  margin: 0 auto;
  padding: 0 60px;
}

@media (max-width: 1199px) {
  .container-narrow {
    padding: 0 60px;
  }
}

@media (max-width: 767px) {
  .container-narrow {
    padding: 0 20px;
  }
}

@media (max-width: 479px) {
  .container-narrow {
    padding: 0 16px;
  }
}

.container-wide {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 60px;
}

@media (max-width: 1199px) {
  .container-wide {
    padding: 0 60px;
  }
}

@media (max-width: 767px) {
  .container-wide {
    padding: 0 20px;
  }
}

@media (max-width: 479px) {
  .container-wide {
    padding: 0 16px;
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Alibaba PuHuiTi 2.0", "Inter", "PingFang SC", "Helvetica Neue", sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  font-feature-settings: 'kern' 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  color: #313131;
  background: #F3F1F4;
}

body.font-loading {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

body.fonts-loaded {
  transition: font-family 0.3s ease;
}

body.fonts-error {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

#app {
  min-height: 100vh;
}

.main-content {
  padding-top: 92px;
}

@media (max-width: 767px) {
  .main-content {
    padding-top: 72px;
  }
}

a {
  text-decoration: none;
}

.text-center {
  text-align: center;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.transition-all {
  transition: all 0.3s ease;
}

.transition-bounce {
  transition: transform 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.shadow-light {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.shadow-medium {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.shadow-dark {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(49, 49, 49, 0.3) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: rgba(49, 49, 49, 0.3);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: rgba(49, 49, 49, 0.5);
}

.btn-reset {
  border: none;
  background: transparent;
  padding: 0;
  margin: 0;
  cursor: pointer;
  outline: none;
}

.img-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.img-contain {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

@media (max-width: 479px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (max-width: 767px) {
  .hide-tablet {
    display: none !important;
  }
}

@media (min-width: 1200px) {
  .hide-desktop {
    display: none !important;
  }
}

.show-mobile {
  display: none !important;
}

@media (max-width: 479px) {
  .show-mobile {
    display: block !important;
  }
}

.show-tablet {
  display: none !important;
}

@media (max-width: 767px) {
  .show-tablet {
    display: block !important;
  }
}

.show-desktop {
  display: none !important;
}

@media (min-width: 1200px) {
  .show-desktop {
    display: block !important;
  }
}
