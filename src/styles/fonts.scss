// ===== 字体定义 =====

// Alibaba PuHuiTi 2.0 Thin (200)
@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url('/fonts/AlibabaPuHuiTi-2-35-Thin.woff2') format('woff2');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

// Alibaba PuHuiTi 2.0 Light (300)
@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url('/fonts/AlibabaPuHuiTi-2-45-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

// Alibaba PuHuiTi 2.0 Regular (400)
@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url('/fonts/AlibabaPuHuiTi-2-55-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

// Alibaba PuHuiTi 2.0 Medium (500)
@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url('/fonts/AlibabaPuHuiTi-2-65-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

// Alibaba PuHuiTi 2.0 SemiBold (600) - 使用 Medium 作为 fallback
@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url('/fonts/AlibabaPuHuiTi-2-65-Medium.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

// Alibaba PuHuiTi 2.0 Bold (700)
@font-face {
  font-family: 'Alibaba PuHuiTi 2.0';
  src: url('/fonts/AlibabaPuHuiTi-2-85-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

// Inter Variable Font - 支持完整字重范围 (100-900)
@font-face {
  font-family: 'Inter';
  src: url('/fonts/Inter-VariableFont.woff2') format('woff2-variations');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
  font-named-instance: 'Regular';
}

// Inter 字重映射 - 利用可变字体的精确字重控制
// 100 - Thin
// 200 - ExtraLight
// 300 - Light
// 400 - Regular
// 500 - Medium
// 600 - SemiBold
// 700 - Bold
// 800 - ExtraBold
// 900 - Black

// ===== 字体变量更新 =====
// 中文字体 - Alibaba PuHuiTi 2.0
$font-family-chinese: 'Alibaba PuHuiTi 2.0', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;

// 英文字体 - Inter Regular
$font-family-english: 'Inter', 'Helvetica Neue', 'Arial', sans-serif;

// 主要字体 - 中文优先
$font-family-primary: 'Alibaba PuHuiTi 2.0', 'Inter', 'PingFang SC', 'Helvetica Neue', sans-serif;

// 次要字体 - 英文优先
$font-family-secondary: 'Inter', 'Alibaba PuHuiTi 2.0', 'Helvetica Neue', 'PingFang SC', sans-serif;

// 默认字体 - Alibaba PuHuiTi 2.0
$font-family-default: 'Alibaba PuHuiTi 2.0', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;

// ===== 字体权重映射 =====
// Alibaba PuHuiTi 2.0 可用字重
$font-weight-light: 300;      // Light
$font-weight-normal: 400;     // Regular (fallback)
$font-weight-medium: 500;     // Medium (fallback)
$font-weight-semibold: 600;   // SemiBold (fallback)
$font-weight-bold: 700;       // Bold (fallback)

// ===== 字体混入 =====

// 中文字体混入
@mixin font-chinese($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.5) {
  font-family: $font-family-chinese;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  font-feature-settings: 'kern' 1;
}

// 英文字体混入 - Inter Variable Font
@mixin font-english($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.4) {
  font-family: $font-family-english;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  font-variation-settings: 'wght' #{$weight};
}

// Inter Variable Font 专用混入 - 支持精确字重控制
@mixin font-inter($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.4) {
  font-family: 'Inter', 'Helvetica Neue', 'Arial', sans-serif;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1, 'case' 1, 'cpsp' 1, 'frac' 1, 'ss01' 1, 'ss02' 1;
  font-variation-settings: 'wght' #{$weight};
  font-optical-sizing: auto;
}

// 主要字体混入（中文优先）
@mixin font-primary($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.5) {
  font-family: $font-family-primary;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  font-feature-settings: 'kern' 1;
}

// 次要字体混入（英文优先）
@mixin font-secondary($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.4) {
  font-family: $font-family-secondary;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  font-feature-settings: 'kern' 1;
}

// 默认字体混入
@mixin font-default($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.5) {
  font-family: $font-family-default;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  font-feature-settings: 'kern' 1;
}

// ===== 特殊字体样式 =====

// 标题字体（中文）
@mixin font-heading($size: $font-size-2xl, $weight: $font-weight-light) {
  @include font-chinese($size, $weight, 1.2);
  letter-spacing: 0.02em;
}

// 正文字体（混合）
@mixin font-body($size: $font-size-base, $weight: $font-weight-normal) {
  @include font-primary($size, $weight, 1.6);
}

// 按钮字体（英文优先）
@mixin font-button($size: $font-size-base, $weight: $font-weight-normal) {
  @include font-secondary($size, $weight, 1);
  letter-spacing: 0.01em;
}

// 代码字体
@mixin font-code($size: $font-size-sm) {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: $size;
  font-weight: $font-weight-normal;
  line-height: 1.4;
}

// ===== Inter Variable Font 特殊用途混入 =====

// Inter 数字字体 - 优化数字显示
@mixin font-inter-numeric($size: $font-size-base, $weight: $font-weight-normal) {
  @include font-inter($size, $weight, 1.2);
  font-feature-settings: 'kern' 1, 'tnum' 1, 'case' 1;
  font-variant-numeric: tabular-nums;
}

// Inter 标题字体 - 优化大字号显示
@mixin font-inter-display($size: $font-size-3xl, $weight: $font-weight-medium) {
  @include font-inter($size, $weight, 1.1);
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1, 'case' 1, 'ss01' 1;
  letter-spacing: -0.02em;
}

// Inter 按钮字体 - 优化按钮文字
@mixin font-inter-button($size: $font-size-base, $weight: $font-weight-medium) {
  @include font-inter($size, $weight, 1);
  font-feature-settings: 'kern' 1, 'case' 1, 'cpsp' 1;
  letter-spacing: 0.01em;
  text-transform: none;
}

// Inter 小字体 - 优化小字号显示
@mixin font-inter-caption($size: $font-size-sm, $weight: $font-weight-normal) {
  @include font-inter($size, $weight, 1.3);
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
}

// ===== 字体优化 =====

// 字体渲染优化
@mixin font-smoothing {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

// 字体加载优化
@mixin font-loading {
  font-display: swap;
}

// 字体加载状态混入
@mixin font-loading-fallback {
  .font-loading &,
  .fonts-error & {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  }

  .fonts-loaded & {
    transition: font-family 0.3s ease;
  }
}

// 安全字体混入（带 fallback 保护）
@mixin font-safe-chinese($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.5) {
  @include font-chinese($size, $weight, $line-height);
  @include font-loading-fallback;
}

@mixin font-safe-english($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.4) {
  @include font-english($size, $weight, $line-height);
  @include font-loading-fallback;
}

@mixin font-safe-primary($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.5) {
  @include font-primary($size, $weight, $line-height);
  @include font-loading-fallback;
}

@mixin font-safe-secondary($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.4) {
  @include font-secondary($size, $weight, $line-height);
  @include font-loading-fallback;
}

// Inter Variable Font 安全混入
@mixin font-safe-inter($size: $font-size-base, $weight: $font-weight-normal, $line-height: 1.4) {
  @include font-inter($size, $weight, $line-height);
  @include font-loading-fallback;
}

// ===== 响应式字体 =====

// 响应式标题字体
@mixin font-responsive-heading($desktop-size, $tablet-size: null, $mobile-size: null) {
  @include font-heading($desktop-size);
  
  @if $tablet-size {
    @include tablet-only {
      font-size: $tablet-size;
    }
  }
  
  @if $mobile-size {
    @include mobile-only {
      font-size: $mobile-size;
    }
  }
}

// 响应式正文字体
@mixin font-responsive-body($desktop-size, $tablet-size: null, $mobile-size: null) {
  @include font-body($desktop-size);
  
  @if $tablet-size {
    @include tablet-only {
      font-size: $tablet-size;
    }
  }
  
  @if $mobile-size {
    @include mobile-only {
      font-size: $mobile-size;
    }
  }
}
