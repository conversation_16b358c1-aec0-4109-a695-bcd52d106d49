// ===== 导入 Sass 基础文件 =====
@import "variables";
@import "fonts";
@import "mixins";

// ===== 导入组件样式 =====
@import "container";

// ===== 全局基础样式 =====
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  @include font-primary;
  @include font-smoothing;
  color: $text-primary;
  background: $bg-white;

  // 字体加载期间使用系统字体，确保页面不空白
  &.font-loading {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  }

  // 字体加载完成后的过渡效果
  &.fonts-loaded {
    transition: font-family 0.3s ease;
  }

  // 字体加载失败时的样式
  &.fonts-error {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  }
}

#app {
  min-height: 100vh;
}

.main-content {
  padding-top: $nav-height-desktop;

  @include tablet-only {
    padding-top: $nav-height-mobile;
  }
}

a{text-decoration: none}

// ===== 通用工具类 =====
.text-center {
  @include text-center;
}

.text-truncate {
  @include text-truncate;
}

.flex-center {
  @include flex-center;
}

.flex-between {
  @include flex-between;
}

.flex-column {
  @include flex-column;
}

.flex-column-center {
  @include flex-column-center;
}

// ===== 动画类 =====
.transition-all {
  @include transition;
}

.transition-bounce {
  @include transition-bounce;
}

.hover-lift {
  @include hover-lift;
}

.hover-scale {
  @include hover-scale;
}

// ===== 阴影类 =====
.shadow-light {
  @include shadow-light;
}

.shadow-medium {
  @include shadow-medium;
}

.shadow-dark {
  @include shadow-dark;
}

// ===== 滚动条样式 =====
.scrollbar-thin {
  @include scrollbar-thin;
}

// ===== 按钮重置 =====
.btn-reset {
  @include button-reset;
}

// ===== 图片样式 =====
.img-cover {
  @include image-cover;
}

.img-contain {
  @include image-contain;
}

// ===== 响应式显示/隐藏 =====
.hide-mobile {
  @include mobile-only {
    display: none !important;
  }
}

.hide-tablet {
  @include tablet-only {
    display: none !important;
  }
}

.hide-desktop {
  @include desktop-up {
    display: none !important;
  }
}

.show-mobile {
  display: none !important;

  @include mobile-only {
    display: block !important;
  }
}

.show-tablet {
  display: none !important;

  @include tablet-only {
    display: block !important;
  }
}

.show-desktop {
  display: none !important;

  @include desktop-up {
    display: block !important;
  }
}
