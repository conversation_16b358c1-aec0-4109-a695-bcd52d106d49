# 字体系统使用指南

## 字体配置概述

项目使用了两种主要字体：
- **中文字体**: Alibaba PuHuiTi 2.0 (不同字重)
- **英文字体**: AvantGarde CE Regular
- **默认字体**: Alibaba PuHuiTi 2.0

## 字体文件

### 可用字体文件
```
public/fonts/
├── AlibabaPuHuiTi-2-45-Light.woff2    # Alibaba PuHuiTi 2.0 Light
└── AvantGarde CE Regular.ttf          # AvantGarde CE Regular
```

### 字体定义
所有字体已在 `src/styles/fonts.scss` 中定义，支持以下字重：
- Light (300)
- Regular (400) 
- Medium (500)
- SemiBold (600)
- Bold (700)

**注意**: 目前只有 Light 字重的实际文件，其他字重使用 Light 作为 fallback。

## 字体变量

### 字体族变量
```scss
$font-family-chinese    // 中文字体 - Alibaba PuHuiTi 2.0
$font-family-english    // 英文字体 - AvantGarde CE
$font-family-primary    // 主要字体 - 中文优先
$font-family-secondary  // 次要字体 - 英文优先
$font-family-default    // 默认字体 - Alibaba PuHuiTi 2.0
```

### 字重变量
```scss
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
```

## 字体混入

### 基础字体混入
```scss
@include font-chinese($size, $weight, $line-height);    // 中文字体
@include font-english($size, $weight, $line-height);    // 英文字体
@include font-primary($size, $weight, $line-height);    // 主要字体（中文优先）
@include font-secondary($size, $weight, $line-height);  // 次要字体（英文优先）
@include font-default($size, $weight, $line-height);    // 默认字体
```

### 特殊用途混入
```scss
@include font-heading($size, $weight);                  // 标题字体（中文）
@include font-body($size, $weight);                     // 正文字体（混合）
@include font-button($size, $weight);                   // 按钮字体（英文优先）
@include font-code($size);                              // 代码字体
```

### 响应式字体混入
```scss
@include font-responsive-heading($desktop, $tablet, $mobile);
@include font-responsive-body($desktop, $tablet, $mobile);
```

### 字体优化混入
```scss
@include font-smoothing;    // 字体渲染优化
@include font-loading;      // 字体加载优化
```

## 使用场景和最佳实践

### 1. 页面标题
```scss
.page-title {
  @include font-heading($font-size-4xl, $font-weight-light);
  // 或使用响应式
  @include font-responsive-heading($font-size-4xl, $font-size-3xl, $font-size-2xl);
}
```

### 2. 正文内容
```scss
.content {
  @include font-body($font-size-base, $font-weight-normal);
  // 中英文混合内容，自动选择合适字体
}
```

### 3. 按钮文字
```scss
.button {
  @include font-button($font-size-base, $font-weight-medium);
  // 英文优先，适合按钮文字
}
```

### 4. 纯中文内容
```scss
.chinese-text {
  @include font-chinese($font-size-lg, $font-weight-normal);
  // 强制使用中文字体
}
```

### 5. 纯英文内容
```scss
.english-text {
  @include font-english($font-size-lg, $font-weight-normal);
  // 强制使用英文字体
}
```

## 组件中的使用示例

### Vue 组件示例
```vue
<template>
  <div class="article">
    <h1 class="article__title">文章标题 Article Title</h1>
    <p class="article__content">
      这是文章内容，包含中英文混合文字。
      This is article content with mixed Chinese and English text.
    </p>
    <button class="article__button">阅读更多 Read More</button>
  </div>
</template>

<style lang="scss" scoped>
.article {
  &__title {
    @include font-heading($font-size-2xl, $font-weight-medium);
    color: $text-primary;
    margin-bottom: $spacing-lg;
  }
  
  &__content {
    @include font-body($font-size-base, $font-weight-normal);
    color: $text-secondary;
    line-height: 1.6;
    margin-bottom: $spacing-xl;
  }
  
  &__button {
    @include font-button($font-size-base, $font-weight-medium);
    background: $primary-color;
    color: $text-white;
    padding: $spacing-sm $spacing-lg;
    border-radius: $border-radius-md;
  }
}
</style>
```

## 字体加载优化

### 1. 字体预加载
在 `index.html` 中添加字体预加载：
```html
<link rel="preload" href="/fonts/AlibabaPuHuiTi-2-45-Light.woff2" as="font" type="font/woff2" crossorigin>
<link rel="preload" href="/fonts/AvantGarde CE Regular.ttf" as="font" type="font/truetype" crossorigin>
```

### 2. 字体显示策略
所有字体使用 `font-display: swap` 策略，确保文字快速显示。

### 3. 字体渲染优化
全局应用了字体平滑和渲染优化：
```scss
body {
  @include font-smoothing;
}
```

## 注意事项

### 1. 字重限制
- 目前只有 Alibaba PuHuiTi 2.0 Light (300) 的实际字体文件
- 其他字重会 fallback 到 Light，但 CSS 仍会应用相应的 font-weight
- 如需更多字重，请添加相应的字体文件

### 2. 字体 Fallback
- 中文字体 fallback: PingFang SC → Hiragino Sans GB → Microsoft YaHei → sans-serif
- 英文字体 fallback: Helvetica Neue → Arial → sans-serif

### 3. 性能考虑
- 字体文件较大，建议使用 CDN 或字体子集化
- 考虑使用 font-display: swap 避免 FOIT (Flash of Invisible Text)

### 4. 浏览器兼容性
- WOFF2 格式支持现代浏览器
- TTF 格式提供更好的兼容性
- 建议提供多种格式的 fallback

## 扩展字体

### 添加新字重
1. 将字体文件放入 `public/fonts/` 目录
2. 在 `src/styles/fonts.scss` 中添加 @font-face 定义
3. 更新相应的字重变量和混入

### 添加新字体
1. 定义新的字体族变量
2. 创建相应的 @font-face 规则
3. 添加专用的混入函数
4. 更新文档和示例

## 调试和测试

### 字体加载检查
使用浏览器开发者工具的 Network 面板检查字体文件是否正确加载。

### 字体渲染测试
使用 FontExample.vue 组件测试不同字体的渲染效果：
```vue
<FontExample />
```

### 跨浏览器测试
在不同浏览器中测试字体渲染效果，确保 fallback 字体正常工作。
