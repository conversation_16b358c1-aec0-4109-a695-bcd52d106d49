<template>
  <div class="simple-test">
    <h1>简单Mock数据测试</h1>
    
    <div class="test-section">
      <h2>直接测试Mock数据</h2>
      <button @click="testMockData">测试Mock数据</button>
      
      <div v-if="mockResult" class="result">
        <h3>Mock数据:</h3>
        <pre>{{ JSON.stringify(mockResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>测试环境变量</h2>
      <button @click="testEnv">检查环境变量</button>
      
      <div v-if="envResult" class="result">
        <h3>环境信息:</h3>
        <pre>{{ JSON.stringify(envResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>测试API函数</h2>
      <button @click="testAPI" :disabled="loading">
        {{ loading ? '测试中...' : '测试API函数' }}
      </button>
      
      <div v-if="apiResult" class="result">
        <h3>API结果:</h3>
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
      
      <div v-if="error" class="error">
        <h3>错误:</h3>
        <pre>{{ error }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { mockData, createMockResponse, mockDelay } from '@/mock/index'
import { getPersonList } from '@/api/home'

const loading = ref(false)
const error = ref('')
const mockResult = ref(null)
const envResult = ref(null)
const apiResult = ref(null)

const testMockData = () => {
  console.log('测试Mock数据...')
  mockResult.value = {
    personList: mockData.personList,
    artists: mockData.artists,
    news: mockData.news
  }
  console.log('Mock数据:', mockResult.value)
}

const testEnv = () => {
  console.log('测试环境变量...')
  envResult.value = {
    NODE_ENV: import.meta.env.NODE_ENV,
    DEV: import.meta.env.DEV,
    PROD: import.meta.env.PROD,
    VITE_USE_MOCK: import.meta.env.VITE_USE_MOCK,
    VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL
  }
  console.log('环境变量:', envResult.value)
}

const testAPI = async () => {
  try {
    loading.value = true
    error.value = ''
    console.log('测试API函数...')
    
    // 直接调用API函数
    const result = await getPersonList({ type: 'artist' })
    console.log('API函数返回:', result)
    
    apiResult.value = result
  } catch (err) {
    console.error('API函数错误:', err)
    error.value = err.message || err.toString()
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.simple-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 15px;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.result {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 10px;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 4px;
  margin-top: 10px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 12px;
  line-height: 1.4;
}

h1, h2, h3 {
  color: #333;
}
</style>
