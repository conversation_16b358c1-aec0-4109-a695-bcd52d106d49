<template>
  <div class="album-detail-page">
    <!-- Navigation is already included in App.vue -->

    <!-- Loading state -->
    <div v-if="loading" class="loading-indicator">
      <div class="loading-text">加载中...</div>
    </div>

    <!-- Main content -->
    <div v-else-if="albumData" class="album-detail-content">
      <!-- Title Section -->
      <div class="title-section">
        <div class="title-content">
          <h1 class="album-title">{{ albumData.title }}</h1>
          <p class="artist-name">{{ albumData.artist }}</p>
        </div>
        <div class="back-section" @click="goBack">
          <div class="back-button">
            <div class="back-icon">
              <div class="back-line"></div>
              <div class="back-arrow-left"></div>
              <div class="back-arrow-right"></div>
              <div class="back-arrow-bottom"></div>
            </div>
          </div>
          <span class="back-text">所有专辑</span>
        </div>
      </div>

      <!-- Album Info Section -->
      <div class="album-info-section">
        <div class="info-item">
          <span class="info-label">语言</span>
          <span class="info-value">{{ albumData.lagurage || "韩语" }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">唱片公司</span>
          <span class="info-value">{{
            albumData.company || "Sea Track Records"
          }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">唱片类型</span>
          <span class="info-value">{{ albumData.type || "EP" }}</span>
        </div>
      </div>

      <!-- Album Cover -->
      <div class="album-cover-section">
        <div class="album-cover">
          <img :src="albumData.cover" :alt="albumData.title" />
        </div>
      </div>

      <!-- Track List Title -->
      <div class="section-title">
        <h2 class="section-heading">专辑曲目单</h2>
      </div>

      <!-- Track List -->
      <div class="track-list-section">
        <div
          v-for="(song, index) in albumData.songList"
          :key="song.id"
          class="track-item"
          :class="{ 'track-item-first': index === 0 }"
        >
          <div class="track-number">
            {{ String(index + 1).padStart(2, "0") }}
          </div>
          <div class="track-info">
            <h3 class="track-title">{{ song.songName }}</h3>
          </div>
        </div>
      </div>

      <!-- Song Introduction Title -->
      <div class="section-title">
        <h2 class="section-heading">歌曲介绍</h2>
      </div>

      <!-- Song Introduction List -->
      <div class="song-intro-section">
        <div
          v-for="(song, index) in songsWithDescription"
          :key="song.id"
          class="song-intro-item"
        >
          <div class="song-intro-number">
            {{ String(index + 1).padStart(2, "0") }}
          </div>
          <div class="song-intro-content">
            <div class="song-intro-header">
              <h3 class="song-intro-title">
                {{ song.songName }}{{ song.tag ? `（${song.tag}）` : "" }}
              </h3>
            </div>
            <p class="song-intro-description">{{ song.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Error state -->
    <div v-else class="error-state">
      <div class="error-text">专辑数据加载失败</div>
    </div>

    <!-- Footer is already included in App.vue -->
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getAlbumDetail } from "@/api/music";

const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(true);
const albumData = ref(null);
const id = route.params.id;

// Computed properties
const songsWithDescription = computed(() => {
  if (!albumData.value?.songList) return [];
  return albumData.value.songList.filter((song) => song.description);
});

// Methods
async function fetchAlbumDetail() {
  try {
    loading.value = true;
    const res = await getAlbumDetail({ id: id });
    console.log("专辑详情数据", res);
    albumData.value = res.data || res;
  } catch (error) {
    console.error("专辑详情加载错误", error);
    albumData.value = null;
  } finally {
    loading.value = false;
  }
}

const goBack = () => {
  router.push("/albums");
};

// Lifecycle
onMounted(() => {
  fetchAlbumDetail();
});
</script>

<style lang="scss" scoped>
.album-detail-page {
  background: $bg-primary;
  min-height: 100vh;
  padding-top: $nav-height-desktop;

  @include tablet-only {
    padding-top: $nav-height-mobile;
  }
}

// Loading and error states
.loading-indicator,
.error-state {
  @include flex-center;
  min-height: 50vh;

  .loading-text,
  .error-text {
    @include font-chinese($font-size-lg, $font-weight-normal);
    color: $text-secondary;
  }
}

.album-detail-content {
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $container-padding-desktop;

  @include desktop-only {
    padding: 0 $container-padding-tablet;
  }

  @include tablet-only {
    padding: 0 $container-padding-mobile;
  }

  @include mobile-only {
    padding: 0 $container-padding-small;
  }
}

// Title Section
.title-section {
  @include flex-baseline;
  gap: $spacing-sm;
  padding: $spacing-3xl 0;
  border-bottom: 1px solid $text-light;
  margin-bottom: $spacing-3xl;

  @include tablet-only {
    @include flex-column;
    align-items: flex-start;
    gap: $spacing-lg;
    padding: $spacing-2xl 0;
    margin-bottom: $spacing-2xl;
  }
}

.title-content {
  @include flex-column;
  gap: $spacing-lg;
}

.album-title {
  @include font-chinese(100px, $font-weight-light);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include tablet-only {
    font-size: 80px;
  }

  @include mobile-only {
    font-size: 60px;
  }
}

.artist-name {
  @include font-chinese(32px, $font-weight-black);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include tablet-only {
    font-size: 28px;
  }

  @include mobile-only {
    font-size: 24px;
  }
}

// Back Section
.back-section {
  @include flex-center;
  gap: $spacing-xs;
  cursor: pointer;
  @include transition;

  &:hover {
    opacity: 0.7;
  }
}

.back-button {
  width: 28px;
  height: 28px;
  position: relative;
  @include flex-center;
}

.back-icon {
  position: relative;
  width: 100%;
  height: 100%;
}

.back-line {
  position: absolute;
  top: 50%;
  left: 6px;
  width: 15px;
  height: 0;
  border-top: 1.5px solid $text-primary;
  transform: translateY(-50%);
}

.back-arrow-left,
.back-arrow-right,
.back-arrow-bottom {
  position: absolute;
  background: $text-primary;
}

.back-arrow-left {
  top: 6.93px;
  left: 6.43px;
  width: 14.14px;
  height: 14.14px;
  transform: rotate(45deg);
  border: 1.5px solid $text-primary;
  border-right: none;
  border-bottom: none;
}

.back-arrow-right {
  top: 6px;
  right: 1px;
  width: 3px;
  height: 16px;
  background: $text-primary;
}

.back-arrow-bottom {
  bottom: 6px;
  left: 1px;
  width: 3px;
  height: 16px;
  background: $text-primary;
}

.back-text {
  @include font-chinese($font-size-xl, $font-weight-normal);
  color: $text-primary;
  line-height: 1em;
}

// Album Info Section
.album-info-section {
  @include flex-between;
  gap: $spacing-4xl;
  margin-bottom: $spacing-3xl;

  @include tablet-only {
    @include flex-column;
    gap: $spacing-lg;
    margin-bottom: $spacing-2xl;
  }
}

.info-item {
  @include flex-column;
  gap: $spacing-sm;
}

.info-label {
  @include font-chinese(16px, $font-weight-bold);
  color: $text-primary;
  line-height: 1em;
}

.info-value {
  @include font-chinese($font-size-xl, $font-weight-normal);
  color: $text-primary;
  line-height: 1em;
}

// Album Cover Section
.album-cover-section {
  @include flex-center;
  margin-bottom: $spacing-3xl;

  @include tablet-only {
    margin-bottom: $spacing-2xl;
  }
}

.album-cover {
  width: 600px;
  height: 600px;
  border-radius: 10px;
  overflow: hidden;

  @include tablet-only {
    width: 400px;
    height: 400px;
  }

  @include mobile-only {
    width: 300px;
    height: 300px;
  }

  img {
    @include image-cover;
  }
}

// Section Titles
.section-title {
  @include flex-baseline;
  gap: $spacing-sm;
  padding-bottom: $spacing-3xl;
  margin-bottom: $spacing-3xl;

  @include tablet-only {
    padding-bottom: $spacing-2xl;
    margin-bottom: $spacing-2xl;
  }
}

.section-heading {
  @include font-chinese(100px, $font-weight-light);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include tablet-only {
    font-size: 80px;
  }

  @include mobile-only {
    font-size: 60px;
  }
}

// Track List Section
.track-list-section {
  @include flex-column;
  margin-bottom: $spacing-4xl;

  @include tablet-only {
    margin-bottom: $spacing-3xl;
  }
}

.track-item {
  @include flex-between;
  align-items: flex-end;
  gap: $spacing-3xl;
  padding: $spacing-xl 0;
  border-top: 1px solid $text-light;

  &.track-item-first {
    border-top: 1px solid $text-primary;
  }

  @include tablet-only {
    gap: $spacing-lg;
    padding: $spacing-lg 0;
  }
}

.track-number {
  font-family: "AvantGarde CE", sans-serif;
  font-size: 120px;
  font-weight: 400;
  line-height: 1em;
  color: $text-primary;

  @include tablet-only {
    font-size: 80px;
  }

  @include mobile-only {
    font-size: 60px;
  }
}

.track-info {
  @include flex-column;
  gap: $spacing-xl;
  flex: 1;
}

.track-title {
  @include font-chinese(32px, $font-weight-bold);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include tablet-only {
    font-size: 28px;
  }

  @include mobile-only {
    font-size: 24px;
  }
}

// Song Introduction Section
.song-intro-section {
  @include flex-column;
  gap: $spacing-4xl;
  margin-bottom: $spacing-4xl;

  @include tablet-only {
    gap: $spacing-3xl;
    margin-bottom: $spacing-3xl;
  }
}

.song-intro-item {
  @include flex-between;
  align-items: flex-start;
  gap: $spacing-3xl;

  @include tablet-only {
    @include flex-column;
    gap: $spacing-lg;
  }
}

.song-intro-number {
  font-family: "AvantGarde CE", sans-serif;
  font-size: 120px;
  font-weight: 400;
  line-height: 1em;
  color: $text-primary;
  flex-shrink: 0;

  @include tablet-only {
    font-size: 80px;
  }

  @include mobile-only {
    font-size: 60px;
  }
}

.song-intro-content {
  @include flex-column;
  gap: $spacing-3xl;
  flex: 1;
  max-width: 960px;

  @include tablet-only {
    gap: $spacing-lg;
  }
}

.song-intro-header {
  @include flex-between;
  align-items: center;
  padding-bottom: $spacing-lg;
  border-bottom: 1px solid $text-primary;

  @include tablet-only {
    border-bottom: 1px solid $text-light;
  }
}

.song-intro-title {
  @include font-chinese(32px, $font-weight-bold);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include tablet-only {
    font-size: 28px;
  }

  @include mobile-only {
    font-size: 24px;
  }
}

.song-intro-description {
  @include font-chinese($font-size-xl, $font-weight-light);
  color: $text-light;
  line-height: 2em;
  margin: 0;

  @include tablet-only {
    font-size: $font-size-lg;
    line-height: 1.8em;
  }

  @include mobile-only {
    font-size: $font-size-base;
    line-height: 1.6em;
  }
}

// Responsive adjustments
@include mobile-only {
  .album-detail-content {
    padding-top: $spacing-lg;
  }

  .title-section {
    padding: $spacing-lg 0;
    margin-bottom: $spacing-lg;
  }

  .album-info-section {
    margin-bottom: $spacing-lg;
  }

  .album-cover-section {
    margin-bottom: $spacing-lg;
  }

  .section-title {
    padding-bottom: $spacing-lg;
    margin-bottom: $spacing-lg;
  }

  .track-list-section {
    margin-bottom: $spacing-2xl;
  }

  .song-intro-section {
    gap: $spacing-2xl;
    margin-bottom: $spacing-2xl;
  }
}
</style>
