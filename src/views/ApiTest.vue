<template>
  <div class="api-test">
    <h1>API测试页面</h1>

    <div class="test-section">
      <h2>自动测试</h2>
      <button @click="runAutoTest" :disabled="loading">
        {{ loading ? "测试中..." : "运行所有API测试" }}
      </button>
    </div>

    <div class="test-section">
      <h2>艺人列表测试</h2>
      <button @click="testPersonList" :disabled="loading">
        {{ loading ? "加载中..." : "测试获取艺人列表" }}
      </button>

      <div v-if="personListResult" class="result">
        <h3>返回结果:</h3>
        <pre>{{ JSON.stringify(personListResult, null, 2) }}</pre>
      </div>

      <div v-if="error" class="error">
        <h3>错误信息:</h3>
        <pre>{{ error }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>新闻列表测试</h2>
      <button @click="testNewsList" :disabled="loading">
        {{ loading ? "加载中..." : "测试获取新闻列表" }}
      </button>

      <div v-if="newsListResult" class="result">
        <h3>返回结果:</h3>
        <pre>{{ JSON.stringify(newsListResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>Banner测试</h2>
      <button @click="testBanner" :disabled="loading">
        {{ loading ? "加载中..." : "测试获取Banner" }}
      </button>

      <div v-if="bannerResult" class="result">
        <h3>返回结果:</h3>
        <pre>{{ JSON.stringify(bannerResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { getPersonList, getNewsList, getBanner } from "@/api/home";
import { runAllTests } from "@/test-api.js";

const loading = ref(false);
const error = ref("");
const personListResult = ref(null);
const newsListResult = ref(null);
const bannerResult = ref(null);

const runAutoTest = async () => {
  try {
    loading.value = true;
    error.value = "";
    console.log("开始运行自动测试...");

    await runAllTests();

    // 同时获取所有数据用于显示
    const personResult = await getPersonList({ type: "artist" });
    const newsResult = await getNewsList();
    const bannerResult = await getBanner();

    personListResult.value = personResult;
    newsListResult.value = newsResult;
    bannerResult.value = bannerResult;

    console.log("自动测试完成!");
  } catch (err) {
    console.error("自动测试失败:", err);
    error.value = err.message || "自动测试失败";
  } finally {
    loading.value = false;
  }
};

const testPersonList = async () => {
  try {
    loading.value = true;
    error.value = "";
    console.log("开始测试艺人列表API...");

    const result = await getPersonList({ type: "artist" });
    console.log("艺人列表API返回:", result);

    personListResult.value = result;
  } catch (err) {
    console.error("艺人列表API错误:", err);
    error.value = err.message || "请求失败";
  } finally {
    loading.value = false;
  }
};

const testNewsList = async () => {
  try {
    loading.value = true;
    error.value = "";
    console.log("开始测试新闻列表API...");

    const result = await getNewsList();
    console.log("新闻列表API返回:", result);

    newsListResult.value = result;
  } catch (err) {
    console.error("新闻列表API错误:", err);
    error.value = err.message || "请求失败";
  } finally {
    loading.value = false;
  }
};

const testBanner = async () => {
  try {
    loading.value = true;
    error.value = "";
    console.log("开始测试Banner API...");

    const result = await getBanner();
    console.log("Banner API返回:", result);

    bannerResult.value = result;
  } catch (err) {
    console.error("Banner API错误:", err);
    error.value = err.message || "请求失败";
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.api-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 20px;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.result {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 10px;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 4px;
  margin-top: 10px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 12px;
  line-height: 1.4;
}

h1,
h2,
h3 {
  color: #333;
}
</style>
