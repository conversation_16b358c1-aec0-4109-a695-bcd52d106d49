<template>
  <div class="artists-page container">
    <!-- 页面标题和控制栏 -->
    <div class="page-header">
      <h1 class="page-title" v-if="type == 'artist'">
        {{ $t("artists.artist") }}
      </h1>
      <h1 class="page-title" v-else-if="type == 'songwriter'">
        {{ $t("artists.songwriter") }}
      </h1>
      <h1 class="page-title" v-else="type == 'producer'">
        {{ $t("artists.producer") }}
      </h1>
      <div class="controls">
        <!-- 搜索框 -->
        <div class="search-box">
          <div class="search-icon">
            <img src="../assets/imgs/search.png" alt="搜索" />
          </div>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索..."
            class="search-input"
            @keyup.enter="handleSearch"
            @input="handleSearchInput"
          />
        </div>

        <!-- 显示模式切换 -->
        <div class="view-toggle">
          <button
            @click="viewMode = 'single'"
            :class="['toggle-btn', { active: viewMode === 'single' }]"
            title="单人模式"
          >
            <div class="single-icon"></div>
          </button>
          <button
            @click="viewMode = 'list'"
            :class="['toggle-btn', { active: viewMode === 'list' }]"
            title="列表模式"
          >
            <div class="list-icon">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="artists-content">
      <!-- 单人显示模式 -->
      <div v-if="viewMode === 'single'" class="single-view">
        <div class="artist-sidebar">
          <div class="artist-list">
            <div
              v-for="artist in filteredArtists"
              :key="artist.id"
              @click="selectArtist(artist)"
              :class="[
                'artist-item',
                { active: selectedArtist?.id === artist.id },
              ]"
            >
              <h3 class="artist-name">{{ artist.name }}</h3>
            </div>
          </div>
        </div>

        <div class="artist-display">
          <div v-if="selectedArtist" class="artist-image-container">
            <div class="artist-image" :key="selectedArtist.id">
              <img
                :src="selectedArtist.image"
                :alt="selectedArtist.name"
                @click="goToArtistDetail(selectedArtist)"
              />
            </div>
            <div class="artist-info">
              <div class="artist-tags">
                <span class="tag">{{
                  "[" + selectedArtist.category + "]"
                }}</span>
              </div>
            </div>
          </div>
          <div v-else class="no-selection">
            <p>请选择一位</p>
          </div>
        </div>
      </div>

      <!-- 列表显示模式 -->
      <div v-else class="list-view">
        <!-- 有结果时显示网格 -->
        <div v-if="filteredArtists.length > 0" class="artist-grid">
          <div
            v-for="artist in filteredArtists"
            :key="artist.id"
            class="artist-card"
            @click="goToArtistDetail(artist)"
          >
            <div class="card-image">
              <img :src="artist.image" :alt="artist.name" />
            </div>
            <div class="card-content">
              <h3 class="card-title">{{ artist.name }}</h3>
              <div class="card-tags">
                <span class="tag">{{ artist.category }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 搜索结果为空时的提示 -->
        <div v-else-if="isSearching && !loading" class="empty-search-result">
          <div class="empty-icon">🔍</div>
          <h3 class="empty-title">暂时没有查到结果</h3>
          <p class="empty-description">请尝试其他关键词或清空搜索框</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getPersonList, getPerson } from "@/api/home";

const router = useRouter();
const route = useRoute();

// 响应式数据
const searchQuery = ref("");
const viewMode = ref("single"); // 'single' 或 'list'
const selectedArtist = ref(null);
const artists = ref([]);
const loading = ref(false);

// 搜索相关状态
const isSearching = ref(false); // 是否正在搜索状态
const originalViewMode = ref("single"); // 记录搜索前的显示模式
const allArtists = ref([]); // 保存所有数据
const type = route.query.type || "artist";

// 数据
async function fetchPersonList() {
  try {
    const res = await getPersonList({ type: type });
    console.log("数据", res.list);
    // 由于API响应拦截器已经处理了数据格式，直接使用res.list
    const data = res.list || res;
    artists.value = data;
    allArtists.value = data; // 保存所有数据
    // 默认选择第一个
    if (artists.value.length > 0) {
      selectedArtist.value = artists.value[0];
    }
  } catch (e) {
    console.log("数据错误", e);
  }
}

// 搜索功能
const handleSearch = async () => {
  const keyword = searchQuery.value.trim();

  if (!keyword) {
    // 如果搜索框为空，恢复所有数据和原始显示模式
    handleClearSearch();
    return;
  }
  loading.value = true;
  artists.value = [];
  try {
    // 记录搜索前的显示模式
    if (!isSearching.value) {
      originalViewMode.value = viewMode.value;
      console.log("模式", originalViewMode.value);
    }

    isSearching.value = true;
    // 搜索时强制切换到列表模式
    viewMode.value = "list";
    const res = await getPerson({ name: keyword });
    console.log("搜索结果:", res);

    // 处理搜索结果
    const searchResults = res.list || res || [];
    artists.value = searchResults;
    loading.value = false;
    // 清空选中的人（因为切换到列表模式）
    selectedArtist.value = null;
  } catch (e) {
    console.error("搜索失败:", e);
    // 搜索失败时显示空结果
    artists.value = [];
  }
};

const handleSearchInput = () => {
  // 当搜索框被清空时，恢复所有数据
  if (!searchQuery.value.trim()) {
    handleClearSearch();
  }
};

const handleClearSearch = () => {
  isSearching.value = false;
  // 恢复所有数据
  artists.value = allArtists.value;
  // 恢复原始显示模式
  viewMode.value = originalViewMode.value;
  // 如果是单人模式，恢复第一个人物的选中状态
  if (viewMode.value === "single" && artists.value.length > 0) {
    selectedArtist.value = artists.value[0];
  }
};

// 计算属性
const filteredArtists = computed(() => {
  return artists.value;
});

const selectArtist = (artist) => {
  selectedArtist.value = artist;
};

const goToArtistDetail = (artist) => {
  router.push(`/detail/${type}/${artist.id}`);
};
// 生命周期
onMounted(() => {
  fetchPersonList();
});
</script>

<style lang="scss" scoped>
.artists-page {
  // min-height: calc(100vh - #{$nav-height-desktop});
  background: $bg-primary;

  @include tablet-only {
    min-height: calc(100vh - #{$nav-height-mobile});
  }
}

// 页面头部
.page-header {
  @include flex-baseline;
  margin-bottom: $spacing-3xl;
  gap: $spacing-sm;

  @include tablet-only {
    @include flex-column;
    align-items: flex-start;
    gap: $spacing-lg;
  }
}

.page-title {
  @include font-primary($font-size-7xl, $font-weight-light);
  color: $text-primary;
  margin: 0;
  line-height: $font-size-7xl;

  @include medium-only {
    font-size: $font-size-6xl;
    line-height: $font-size-6xl;
  }

  @include tablet-only {
    font-size: $font-size-5xl;
    line-height: $font-size-5xl;
  }

  @include mobile-only {
    font-size: $font-size-4xl;
    line-height: $font-size-4xl;
  }
}

.controls {
  display: flex;
  align-items: center;
  @include mobile-only {
    gap: $spacing-md;
  }
}

/* 搜索框 */
.search-box {
  position: relative;
  display: flex;
  align-items: center;
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-box:hover,
.search-box:focus-within {
  width: 200px;
}

.search-icon {
  position: absolute;
  left: 0;
  top: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;

  img {
    width: 100%;
    height: 100%;
  }
}

.search-input {
  position: absolute;
  left: 28px;
  top: 0;
  width: 85%;
  height: 24px;
  border: none;
  background: transparent;
  outline: none;
  padding-left: 18px;
  font-size: 14px;
  color: #313131;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.search-box:hover .search-input,
.search-box:focus-within .search-input {
  opacity: 1;

  border-bottom: 1px solid $primary-color;
  padding: 0 12px 0 10px;
}

/* 视图切换按钮 */
.view-toggle {
  display: flex;
  gap: 0;
}

/* 单人模式图标 */
.single-icon {
  width: 23px;
  height: 23px;
  background: #a2a2a2;
  border-radius: 2px;
}

/* 列表模式图标 */
.list-icon {
  width: 23px;
  height: 23px;
  display: grid;
  grid-template-columns: repeat(3, 5px);
  grid-template-rows: repeat(3, 5px);
  gap: 4px;
}

.dot {
  width: 5px;
  height: 5px;
  background: #a2a2a2;
  border-radius: 1px;
}
.toggle-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;

  &:hover {
    .single-icon,
    .dot {
      background: #313131;
    }
  }

  &.active {
    .single-icon,
    .dot {
      background: #313131;
    }
  }
}

/*内容区域 */

/* 单人显示模式 */
.single-view {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.artist-sidebar {
  width: 330px;
  height: 600px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.artist-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
  max-height: 600px;
  overflow-y: auto;
  padding: 20px 0;

  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(49, 49, 49, 0.3) transparent;
}

.artist-list::-webkit-scrollbar {
  width: 6px;
}

.artist-list::-webkit-scrollbar-track {
  background: transparent;
}

.artist-list::-webkit-scrollbar-thumb {
  background: rgba(49, 49, 49, 0.3);
  border-radius: 3px;
}

.artist-list::-webkit-scrollbar-thumb:hover {
  background: rgba(49, 49, 49, 0.5);
}

.artist-item {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}
.artist-item.active .artist-name {
  color: #313131;
  font-size: 48px;
  font-weight: 700;
}

.artist-item:not(.active) .artist-name {
  color: #636363;
  font-weight: 400;
}
.artist-item:not(.active) .artist-name:hover {
  color: #313131;
}

.artist-name {
  font-family: "Alibaba PuHuiTi 2.0", sans-serif;
  font-size: 32px;
  line-height: 1em;
  margin: 0 0 8px 0;
  transition: all 0.3s ease;
}

/* 人物展示区域 */
.artist-display {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.artist-image-container {
  width: 100%;
  display: flex;
  flex: 2;
}

.artist-image {
  width: 600px;
  height: 600px;
  overflow: hidden;
  animation: fadeInScale 0.6s ease-out;
}

.artist-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.artist-image:hover img {
  transform: scale(1.05);
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.96);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.artist-info {
  width: 300px;
  position: relative;
}

.artist-tags {
  position: absolute;
  bottom: 0px;
  left: 20px;
}

.tag {
  font-family: "Alibaba PuHuiTi 2.0", sans-serif;
  font-weight: 400;
  font-size: 20px;
  line-height: 1em;
  color: #313131;
  padding: 8px 12px;
  backdrop-filter: blur(10px);
}

.no-selection {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #999;
  font-size: 18px;
}

/* 列表显示模式 */
.list-view {
  width: 100%;
}

.artist-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 13px;
}

.artist-card {
  padding-bottom: 60px;
  background: transparent;
  overflow: visible;
  cursor: pointer;
  transition: all 0.3s ease;
}

.card-image {
  width: 320px;
  height: 320px;
  overflow: hidden;
  border-radius: 4px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease-in-out;
  }
  &:hover {
    animation: bounceScale 0.6s ease-in-out;
  }
}

/* 弹跳缩放动画 - 先缩小再恢复原大小 */
@keyframes bounceScale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

.card-content {
  padding: 20px 0 0 0;
}

.card-title {
  font-family: $font-family-chinese;
  font-weight: 700;
  font-size: 20px;
  line-height: 1em;
  color: #313131;
  margin: 0 0 10px 0;
}

.card-tags {
  display: flex;
  gap: 5px;
}

.card-tags .tag {
  background: transparent;
  color: #868686;
  font-size: 16px;
  padding: 0;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  /* 容器样式已在全局 container.css 中定义 */

  .artist-sidebar {
    width: 280px;
    height: 500px;
  }

  .artist-list {
    max-height: 500px;
  }

  .artist-name {
    font-size: 40px;
  }

  .artist-alias {
    font-size: 28px;
  }

  .artist-image {
    width: 500px;
    height: 500px;
  }
}

@media (max-width: 968px) {
  .page-title {
    font-size: 80px;
  }

  .single-view {
    flex-direction: column;
    gap: 40px;
  }

  .artist-sidebar {
    width: 100%;
    height: auto;
    align-items: flex-start;
  }

  .artist-list {
    flex-direction: row;
    gap: 30px;
    overflow-x: auto;
    overflow-y: visible;
    max-height: none;
    padding: 10px 0;
  }

  .artist-item {
    flex-shrink: 0;
    min-width: 200px;
  }

  .artist-display {
    justify-content: center;
  }

  .artist-image {
    width: 400px;
    height: 400px;
  }

  .artist-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .artists-page {
    min-height: calc(100vh - 72px);
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .page-title {
    font-size: 60px;
  }

  .controls {
    align-self: flex-end;
  }

  .artist-name {
    font-size: 32px;
  }

  .artist-alias {
    font-size: 24px;
  }

  .artist-image {
    width: 100%;
    height: 300px;
    max-width: 300px;
  }

  .artist-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 10px;
  }

  .card-image {
    height: 280px;
  }

  .search-box:hover,
  .search-box:focus-within {
    width: 150px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 48px;
  }

  .artist-sidebar {
    height: auto;
  }

  .artist-list {
    flex-direction: column;
    gap: 15px;
    max-height: 300px;
    overflow-y: auto;
    overflow-x: visible;
  }

  .artist-item {
    min-width: auto;
  }

  .artist-name {
    font-size: 28px;
  }

  .artist-card {
    padding-bottom: 20px;
  }

  .controls {
    gap: 15px;
  }

  .search-box:hover,
  .search-box:focus-within {
    width: 120px;
  }
}

/* 空搜索结果样式 */
.empty-search-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
  min-height: 400px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.6;
}

.empty-title {
  font-family: "Alibaba PuHuiTi 2.0", sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #313131;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.empty-description {
  font-family: "Alibaba PuHuiTi 2.0", sans-serif;
  font-size: 16px;
  color: #666;
  margin: 0;
  line-height: 1.5;
  max-width: 400px;
}

@media (max-width: 768px) {
  .empty-search-result {
    padding: 60px 20px;
    min-height: 300px;
  }

  .empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
  }

  .empty-title {
    font-size: 20px;
    margin-bottom: 12px;
  }

  .empty-description {
    font-size: 14px;
  }
}
</style>
