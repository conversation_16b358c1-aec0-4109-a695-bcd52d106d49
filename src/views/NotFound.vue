<template>
  <div class="not-found">
    <!-- 语言切换器 -->
    <div class="language-switcher-container">
      <LanguageSwitcher />
    </div>
    <div class="not-found-container">
      <div class="error-content">
        <div
          class="error-number"
          :style="{
            background: `linear-gradient(45deg, ${themeColors.primary}, ${themeColors.secondary})`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }"
        >
          404
        </div>
        <div class="error-message">
          <h1>{{ $t("notFound.title") }}</h1>
          <p>{{ $t("notFound.description") }}</p>
        </div>
        <div class="error-actions">
          <button @click="goHome" class="home-button">
            {{ $t("notFound.homeButton") }}
          </button>
          <button @click="goBack" class="back-button">
            {{ $t("notFound.backButton") }}
          </button>
        </div>
      </div>
      <div class="error-illustration">
        <div class="floating-elements">
          <div
            class="element element-1"
            :style="{
              background: `linear-gradient(45deg, ${themeColors.primary}, ${themeColors.secondary})`,
            }"
          ></div>
          <div
            class="element element-2"
            :style="{
              background: `linear-gradient(45deg, ${themeColors.secondary}, ${themeColors.primary})`,
            }"
          ></div>
          <div
            class="element element-3"
            :style="{
              background: `linear-gradient(45deg, ${themeColors.primary}, rgba(134, 134, 134, 0.3))`,
            }"
          ></div>
          <div
            class="element element-4"
            :style="{
              background: `linear-gradient(45deg, ${themeColors.secondary}, ${themeColors.primary})`,
            }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import { computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import { useLanguageStore } from "@/stores/language";
import LanguageSwitcher from "@/components/LanguageSwitcher.vue";

const router = useRouter();
const { locale } = useI18n();
const languageStore = useLanguageStore();

// 根据语言获取主题色彩
const themeColors = computed(() => {
  const colors = {
    zh: { primary: "#313131", secondary: "#868686" },
    en: { primary: "#2c3e50", secondary: "#7f8c8d" },
    ja: { primary: "#e74c3c", secondary: "#c0392b" },
    ko: { primary: "#9b59b6", secondary: "#8e44ad" },
  };
  return colors[locale.value] || colors.zh;
});

// 返回首页
const goHome = () => {
  router.push("/");
};

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    router.push("/");
  }
};

// 页面加载时检测语言
onMounted(() => {
  // 如果当前语言是默认语言，尝试根据浏览器语言设置更合适的语言
  if (locale.value === "zh") {
    const browserLang = navigator.language.toLowerCase();
    if (browserLang.startsWith("en")) {
      languageStore.changeLanguage("en");
    } else if (browserLang.startsWith("ja")) {
      languageStore.changeLanguage("ja");
    } else if (browserLang.startsWith("ko")) {
      languageStore.changeLanguage("ko");
    }
  }
});
</script>

<style lang="scss" scoped>
.not-found {
  min-height: 100vh;
  background: linear-gradient(
    135deg,
    $bg-primary 0%,
    rgba(134, 134, 134, 0.1) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        circle at 20% 80%,
        rgba(134, 134, 134, 0.1) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 20%,
        rgba(49, 49, 49, 0.1) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 40% 40%,
        rgba(134, 134, 134, 0.05) 0%,
        transparent 50%
      );
    z-index: 1;
  }
}

.language-switcher-container {
  position: absolute;
  top: 30px;
  right: 30px;
  z-index: 10;

  @include tablet-only {
    top: 20px;
    right: 20px;
  }

  @media (max-width: 480px) {
    top: 15px;
    right: 15px;
  }
}

.not-found-container {
  display: flex;
  align-items: center;
  gap: 80px;
  max-width: 1200px;
  width: 100%;
  z-index: 2;

  @include tablet-only {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }
}

.error-content {
  flex: 1;
  animation: slideInLeft 0.8s ease-out;
}

.error-number {
  font-family: $font-family-chinese;
  font-weight: 700;
  font-size: 120px;
  line-height: 1em;
  margin-bottom: 30px;
  opacity: 0.8;
  animation: pulse 2s ease-in-out infinite;

  @include tablet-only {
    font-size: 100px;
  }

  @media (max-width: 480px) {
    font-size: 80px;
  }
}

.error-message {
  margin-bottom: 40px;

  h1 {
    font-family: $font-family-chinese;
    font-weight: 700;
    font-size: 48px;
    line-height: 1.2em;
    color: $text-primary;
    margin: 0 0 20px 0;

    @include tablet-only {
      font-size: 36px;
    }

    @media (max-width: 480px) {
      font-size: 28px;
    }
  }

  p {
    font-family: $font-family-chinese;
    font-weight: 300;
    font-size: 20px;
    line-height: 1.6em;
    color: $text-light;
    margin: 0;

    @include tablet-only {
      font-size: 18px;
    }

    @media (max-width: 480px) {
      font-size: 16px;
    }
  }
}

.error-actions {
  display: flex;
  gap: 20px;

  @include tablet-only {
    justify-content: center;
  }

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 15px;
  }
}

.home-button,
.back-button {
  padding: 15px 30px;
  border: none;
  border-radius: 8px;
  font-family: $font-family-chinese;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;

  @media (max-width: 480px) {
    padding: 12px 24px;
    font-size: 14px;
  }
}

.home-button {
  background: $text-primary;
  color: $bg-primary;

  &:hover {
    background: rgba(49, 49, 49, 0.8);
    transform: translateY(-2px);
  }
}

.back-button {
  background: transparent;
  color: $text-primary;
  border: 2px solid $text-primary;

  &:hover {
    background: $text-primary;
    color: $bg-primary;
    transform: translateY(-2px);
  }
}

.error-illustration {
  flex: 1;
  position: relative;
  height: 400px;
  animation: slideInRight 0.8s ease-out;

  @include tablet-only {
    height: 300px;
  }

  @media (max-width: 480px) {
    height: 200px;
  }
}

.floating-elements {
  position: relative;
  width: 100%;
  height: 100%;
}

.element {
  position: absolute;
  border-radius: 50%;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
    transform: scale(1.1);
    animation-play-state: paused;
  }
}

.element-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.element-2 {
  width: 60px;
  height: 60px;
  top: 60%;
  right: 20%;
  animation-delay: 1s;
}

.element-3 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 10%;
  animation-delay: 2s;
}

.element-4 {
  width: 40px;
  height: 40px;
  bottom: 20%;
  left: 30%;
  animation-delay: 0.5s;
}

// 动画关键帧
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 968px) {
  .not-found-container {
    gap: 30px;
  }

  .error-number {
    font-size: 80px;
  }

  .error-message h1 {
    font-size: 32px;
  }

  .error-message p {
    font-size: 16px;
  }
}
</style>
