<template>
  <div class="albums-page container">
    <!-- 页面标题和控制栏 -->
    <div class="page-header">
      <!-- 畅销单曲，发行公用 -->
      <h1 class="page-title">{{ $t("albums.title") }}</h1>
      <div class="controls">
        <!-- 显示模式切换 -->
        <div class="view-toggle">
          <button
            @click="viewMode = 'carousel'"
            :class="['toggle-btn', { active: viewMode === 'carousel' }]"
            :title="$t('albums.carouselMode')"
          >
            <div class="carousel-icon"></div>
          </button>
          <button
            @click="viewMode = 'list'"
            :class="['toggle-btn', { active: viewMode === 'list' }]"
            :title="$t('albums.listMode')"
          >
            <div class="list-icon">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- 专辑内容区域 -->
    <div v-if="loading" class="loading-indicator">
      <div class="loading-text">{{ $t("common.loading") }}</div>
    </div>
    <div v-else>
      <!-- 暂无数据时的提示 -->
      <div v-if="albums.length == 0" class="empty-result">
        <div class="empty-icon">🎵</div>
        <h3 class="empty-title">{{ $t("albums.noData") }}</h3>
        <p class="empty-description">{{ $t("albums.noDataDescription") }}</p>
      </div>
      <div v-else>
        <div class="albums-content">
          <!-- 轮播显示模式 -->
          <div v-if="viewMode === 'carousel'" class="carousel-view">
            <div class="carousel-container">
              <!-- 主要展示区域 -->
              <div class="main-display">
                <div v-if="selectedAlbum" class="album-main">
                  <div class="album-image-large">
                    <img
                      :src="selectedAlbum.cover"
                      :alt="selectedAlbum.title"
                      @load="onImageLoad"
                    />
                  </div>
                  <div class="album-info-large">
                    <h2 class="album-title-large">{{ selectedAlbum.title }}</h2>
                    <p class="album-artist-large">{{ selectedAlbum.artist }}</p>
                  </div>
                </div>
              </div>

              <!-- 侧边专辑列表 -->
              <div class="album-sidebar">
                <div class="album-list">
                  <div
                    v-for="album in albums"
                    :key="album.id"
                    @click="selectAlbum(album)"
                    :class="[
                      'album-item',
                      { active: selectedAlbum?.id === album.id },
                    ]"
                  >
                    <div class="album-cover-small">
                      <img :src="album.cover" :alt="album.title" />
                    </div>
                    <div class="album-info-small">
                      <h3 class="album-title-small">{{ album.title }}</h3>
                      <p class="album-artist-small">{{ album.artist }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 列表显示模式 -->
          <div v-else class="list-view">
            <div v-if="albums.length > 0" class="album-grid">
              <div
                v-for="album in albums"
                :key="album.id"
                class="album-card"
                @click="goToAlbumDetail(album)"
              >
                <div class="card-image">
                  <img :src="album.cover" :alt="album.title" />
                </div>
                <div class="card-content">
                  <h3 class="card-title">{{ album.title }}</h3>
                  <p class="card-artist">{{ album.artist }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getAlbumDetail } from "@/api/music";

const router = useRouter();
const route = useRoute();

// 响应式数据
const viewMode = ref("carousel"); // 'carousel' 或 'list'
const selectedAlbum = ref(null);
const albums = ref([]);
const loading = ref(false);
const id = route.params.id;

// 获取专辑数据
async function fetchAlbumsDetail() {
  try {
    loading.value = true;
    const res = await getAlbumDetail({ id: id });
    console.log("专辑ep数据", res);
    albums.value = res.data || res || [];
    // 默认选择第一个专辑
    if (albums.value.length > 0) {
      selectedAlbum.value = albums.value[0];
    }
  } catch (e) {
    console.log("专辑数据错误", e);
  } finally {
    loading.value = false;
  }
}

const selectAlbum = (album) => {
  selectedAlbum.value = album;
};

const goToAlbumDetail = (album) => {
  router.push(`/albums/${album.id}`);
};

// 生命周期
onMounted(() => {
  fetchAlbumsDetail();
});
</script>

<style lang="scss" scoped>
.albums-page {
  background: $bg-primary;

  @include tablet-only {
    min-height: calc(100vh - #{$nav-height-mobile});
  }
}

// 页面头部
.page-header {
  @include flex-baseline;
  margin-bottom: $spacing-3xl;
  gap: $spacing-sm;

  @include tablet-only {
    @include flex-column;
    align-items: flex-start;
    gap: $spacing-lg;
  }
}

.page-title {
  font-family: $font-family-chinese;
  font-weight: 300;
  font-size: 100px;
  line-height: 1em;
  color: $text-primary;
  margin: 0;

  @include tablet-only {
    font-size: 80px;
  }
}

.controls {
  @include flex-center;
  gap: $spacing-lg;
}

// 视图切换按钮
.view-toggle {
  @include flex-center;
  gap: $spacing-lg;
}

.carousel-icon {
  width: 24px;
  height: 24px;
  background: url(../assets/imgs/carousel.png) no-repeat;
  background-size: cover;
}

.list-icon {
  @include flex-center;
  flex-wrap: wrap;
  width: 23px;
  height: 24px;
  gap: 4px;
}

.dot {
  width: 5px;
  height: 5px;
  background-color: #a2a2a2;
  border-radius: 0;
}

.toggle-btn {
  @include flex-center;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    .carousel-icon {
      background: url(../assets/imgs/carousel-active.png) no-repeat;
      background-size: cover;
    }
    .dot {
      background-color: $primary-color;
    }
  }
  &:hover {
    .carousel-icon {
      background: url(../assets/imgs/carousel-active.png) no-repeat;
      background-size: cover;
    }
    .dot {
      background: $primary-color;
    }
  }
}

// 轮播模式样式
.carousel-view {
  width: 100%;
}

.carousel-container {
  @include flex-center;
  gap: $spacing-xl;
  align-items: flex-start;
}

.main-display {
  flex: 1;
  max-width: 600px;
}

.album-main {
  @include flex-column;
  align-items: center;
  gap: $spacing-lg;
}

.album-image-large {
  width: 600px;
  height: 600px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.album-info-large {
  @include flex-column;
  align-items: center;
  gap: $spacing-sm;
}

.album-title-large {
  font-family: $font-family-chinese;
  font-weight: 700;
  font-size: 48px;
  line-height: 1em;
  color: $text-secondary;
  margin: 0;
}

.album-artist-large {
  font-family: $font-family-chinese;
  font-weight: 700;
  font-size: 32px;
  line-height: 1em;
  color: $text-secondary;
  margin: 0;
}

// 侧边栏样式
.album-sidebar {
  @include flex-column;
  gap: $spacing-lg;
  width: 300px;
}

.album-list {
  @include flex-column;
  gap: $spacing-lg;
  max-height: 600px;
  overflow-y: auto;
}

.album-item {
  @include flex-column;
  align-items: center;
  gap: $spacing-md;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: $spacing-md;
  border-radius: 8px;

  &:hover,
  &.active {
    background-color: rgba($text-secondary, 0.05);
  }
}

.album-cover-small {
  width: 300px;
  height: 300px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.album-info-small {
  @include flex-column;
  align-items: center;
  gap: $spacing-xs;
}

.album-title-small {
  font-family: $font-family-chinese;
  font-weight: 400;
  font-size: 20px;
  line-height: 1em;
  color: $text-secondary;
  margin: 0;
}

.album-artist-small {
  font-family: $font-family-chinese;
  font-weight: 700;
  font-size: 16px;
  line-height: 1em;
  color: $text-secondary;
  margin: 0;
}

// 列表模式样式
.list-view {
  width: 100%;
}

.album-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 13px;
  width: 100%;
  max-width: 1320px;
  margin: 0 auto;
}

.album-card {
  @include flex-column;
  align-items: center;
  padding-bottom: 60px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.card-image {
  width: 320px;
  height: 320px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  &:hover {
    animation: bounceScale 0.6s ease-in-out;
  }
}
/* 弹跳缩放动画 - 先缩小再恢复原大小 */
@keyframes bounceScale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

.card-content {
  @include flex-column;
  align-items: center;
  gap: $spacing-sm;
  padding-top: 20px;
}

.card-title {
  font-family: $font-family-chinese;
  font-weight: 400;
  font-size: 20px;
  line-height: 1em;
  color: $text-primary;
  text-align: center;
}

.card-artist {
  font-family: $font-family-chinese;
  font-weight: 700;
  font-size: 16px;
  line-height: 1em;
  color: $text-primary;
  text-align: center;
}

// 加载状态样式
.loading-indicator {
  @include flex-column;
  align-items: center;
  justify-content: center;
  gap: $spacing-lg;
  padding: $spacing-3xl;
  text-align: center;
  min-height: 400px;

  font-family: $font-family-chinese;
  font-weight: 400;
  font-size: 18px;
  color: $text-secondary;

  position: relative;

  &::before {
    content: "";
    width: 40px;
    height: 40px;
    border: 3px solid rgba(134, 134, 134, 0.3);
    border-top: 3px solid $text-primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: $spacing-md;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 空状态样式
.empty-result {
  @include flex-column;
  align-items: center;
  justify-content: center;
  gap: $spacing-lg;
  padding: $spacing-3xl;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  opacity: 0.5;
}

.empty-title {
  font-family: $font-family-chinese;
  font-weight: 400;
  font-size: 24px;
  color: $text-secondary;
  margin: 0;
}

.empty-description {
  font-family: $font-family-chinese;
  font-weight: 300;
  font-size: 16px;
  color: $text-light;
  margin: 0;
  max-width: 400px;
}

// 响应式设计
@media (max-width: 968px) {
  .page-title {
    font-size: 80px;
  }

  .carousel-container {
    flex-direction: column;
    gap: $spacing-lg;
  }

  .album-sidebar {
    width: 100%;
  }

  .album-list {
    flex-direction: row;
    gap: $spacing-lg;
    overflow-x: auto;
    overflow-y: visible;
    max-height: none;
    padding: $spacing-sm 0;
  }

  .album-item {
    flex-shrink: 0;
    min-width: 200px;
  }

  .album-cover-small {
    width: 200px;
    height: 200px;
  }

  .album-image-large {
    width: 400px;
    height: 400px;
  }

  .album-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
  }

  .card-image {
    width: 280px;
    height: 280px;
  }
}
</style>
