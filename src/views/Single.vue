<template>
  <div class="single-detail-page">
    <!-- Loading state -->
    <div v-if="loading" class="loading-indicator">
      <div class="loading-text">{{ $t("common.loading") }}</div>
    </div>

    <!-- Main content -->
    <div v-else-if="singleData" class="single-detail-content">
      <!-- Title Section -->
      <div class="title-section">
        <div class="title-content">
          <h1 class="single-title">{{ singleData.title }}</h1>
          <p class="artist-name">{{ singleData.artist }}</p>
        </div>
        <div class="back-section" @click="goBack">
          <div class="back-button">
            <img src="../assets/imgs/back.png" alt="Back" />
          </div>
          <span class="back-text">{{ $t("singleDetail.backToSingles") }}</span>
        </div>
      </div>

      <!-- Single Info Section -->
      <div class="single-info-section">
        <div class="info-item">
          <span class="info-label">{{ $t("singleDetail.language") }}</span>
          <span class="info-value">{{ singleData.lagurage || "国语" }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">{{ $t("singleDetail.recordCompany") }}</span>
          <span class="info-value">{{
            singleData.company || "可瑞思國際娛樂股份有限公司"
          }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">{{ $t("singleDetail.albumType") }}</span>
          <span class="info-value">{{ singleData.type || "Single" }}</span>
        </div>
      </div>

      <!-- Single Cover -->
      <div class="single-cover-section">
        <div class="single-cover">
          <img :src="singleData.cover" :alt="singleData.title" />
        </div>
      </div>

      <!-- Song Description -->
      <div class="song-description-section">
        <div class="description-content">
          <p class="description-text">{{ singleData.description }}</p>
        </div>
      </div>
    </div>

    <!-- Error state -->
    <div v-else class="error-state">
      <div class="error-text">{{ $t("common.error") }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getSingle } from "@/api/music";

const route = useRoute();
const router = useRouter();

// Reactive data
const loading = ref(true);
const singleData = ref(null);
const id = route.params.id;

// Methods
async function fetchSingleDetail() {
  try {
    loading.value = true;
    const res = await getSingle({ id: id });
    console.log("单曲详情数据", res);
    singleData.value = res.data || res;
  } catch (error) {
    console.error("单曲详情加载错误", error);
    singleData.value = null;
  } finally {
    loading.value = false;
  }
}

const goBack = () => {
  // 这里可以根据需要跳转到单曲列表页面
  router.go(-1); // 返回上一页
};

// Lifecycle
onMounted(() => {
  fetchSingleDetail();
});
</script>

<style lang="scss" scoped>
.single-detail-page {
  background: $bg-primary;
  min-height: 100vh;
  padding-top: $nav-height-desktop;

  @include tablet-only {
    padding-top: $nav-height-mobile;
  }
}

// Loading and error states
.loading-indicator,
.error-state {
  @include flex-center;
  min-height: 50vh;

  .loading-text,
  .error-text {
    @include font-chinese($font-size-lg, $font-weight-normal);
    color: $text-secondary;
  }
}

.single-detail-content {
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $container-padding-desktop;

  @include desktop-only {
    padding: 0 $container-padding-tablet;
  }

  @include tablet-only {
    padding: 0 $container-padding-mobile;
  }

  @include mobile-only {
    padding: 0 $container-padding-small;
  }
}

// Title Section
.title-section {
  @include flex-baseline;
  align-items: flex-end;
  gap: $spacing-sm;
  padding: $spacing-3xl 0;
  border-bottom: 1px solid $text-light;
  margin-bottom: $spacing-3xl;

  @include tablet-only {
    @include flex-column;
    align-items: flex-start;
    gap: $spacing-lg;
    padding: $spacing-2xl 0;
    margin-bottom: $spacing-2xl;
  }
}

.title-content {
  @include flex-column;
  gap: $spacing-lg;
}

.single-title {
  @include font-chinese(100px, $font-weight-light);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include tablet-only {
    font-size: 80px;
  }

  @include mobile-only {
    font-size: 60px;
  }
}

.artist-name {
  @include font-chinese(32px, $font-weight-black);
  color: $text-primary;
  line-height: 1em;
  margin: 0;

  @include tablet-only {
    font-size: 28px;
  }

  @include mobile-only {
    font-size: 24px;
  }
}

// Back Section
.back-section {
  @include flex-center;
  gap: $spacing-xs;
  cursor: pointer;
  @include transition;
  transition: all 0.3s ease;

  &:hover {
    transform: translateX(-2px);
  }
}

.back-button {
  width: 28px;
  height: 28px;
  position: relative;
  @include flex-center;

  img {
    width: 100%;
    height: 100%;
  }
}

.back-text {
  @include font-chinese($font-size-xl, $font-weight-normal);
  color: $text-primary;
  line-height: 1em;
}

// Single Info Section
.single-info-section {
  @include flex-between;
  gap: $spacing-4xl;
  margin-bottom: $spacing-3xl;

  @include tablet-only {
    @include flex-column;
    gap: $spacing-lg;
    margin-bottom: $spacing-2xl;
  }
}

.info-item {
  @include flex-column;
  gap: $spacing-sm;
}

.info-label {
  @include font-chinese(16px, $font-weight-bold);
  color: $text-primary;
  line-height: 1em;
}

.info-value {
  @include font-chinese($font-size-xl, $font-weight-normal);
  color: $text-primary;
  line-height: 1em;
}

// Single Cover Section
.single-cover-section {
  @include flex-center;
  margin-bottom: $spacing-3xl;

  @include tablet-only {
    margin-bottom: $spacing-2xl;
  }
}

.single-cover {
  width: 600px;
  height: 600px;
  border-radius: 10px;
  overflow: hidden;

  @include tablet-only {
    width: 400px;
    height: 400px;
  }

  @include mobile-only {
    width: 300px;
    height: 300px;
  }

  img {
    @include image-cover;
  }
}

// Song Description Section
.song-description-section {
  @include flex-center;
  margin-bottom: $spacing-4xl;

  @include tablet-only {
    margin-bottom: $spacing-3xl;
  }
}

.description-content {
  max-width: 600px;
  width: 100%;
}

.description-text {
  @include font-chinese($font-size-xl, $font-weight-light);
  color: $text-light;
  line-height: 2em;
  margin: 0;
  white-space: pre-line; // 保持换行格式

  @include tablet-only {
    font-size: $font-size-lg;
    line-height: 1.8em;
  }

  @include mobile-only {
    font-size: $font-size-base;
    line-height: 1.6em;
  }
}

// Responsive adjustments
@include mobile-only {
  .single-detail-content {
    padding-top: $spacing-lg;
  }

  .title-section {
    padding: $spacing-lg 0;
    margin-bottom: $spacing-lg;
  }

  .single-info-section {
    margin-bottom: $spacing-lg;
  }

  .single-cover-section {
    margin-bottom: $spacing-lg;
  }

  .song-description-section {
    margin-bottom: $spacing-2xl;
  }
}
</style>
