/**
 * 字体性能监控工具
 * 监控字体加载性能，提供优化建议
 */

class FontPerformanceMonitor {
  constructor() {
    this.startTime = performance.now();
    this.loadTimes = new Map();
    this.metrics = {
      totalLoadTime: 0,
      fontsLoaded: 0,
      fontsError: 0,
      layoutShifts: 0,
      renderTime: 0
    };
    
    this.init();
  }

  init() {
    // 监听字体加载事件
    document.addEventListener('fontsloaded', this.handleFontsLoaded.bind(this));
    document.addEventListener('fontserror', this.handleFontsError.bind(this));
    
    // 监听布局偏移
    this.observeLayoutShift();
    
    // 监听首次渲染
    this.observeFirstRender();
  }

  /**
   * 记录字体加载开始时间
   */
  markFontLoadStart(fontName) {
    this.loadTimes.set(fontName, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    });
  }

  /**
   * 记录字体加载完成时间
   */
  markFontLoadEnd(fontName) {
    const loadInfo = this.loadTimes.get(fontName);
    if (loadInfo) {
      loadInfo.endTime = performance.now();
      loadInfo.duration = loadInfo.endTime - loadInfo.startTime;
      this.metrics.fontsLoaded++;
    }
  }

  /**
   * 处理字体加载完成事件
   */
  handleFontsLoaded(event) {
    this.metrics.totalLoadTime = performance.now() - this.startTime;
    this.logPerformanceMetrics();
  }

  /**
   * 处理字体加载错误事件
   */
  handleFontsError(event) {
    this.metrics.fontsError++;
    console.warn('字体加载失败，性能可能受到影响');
  }

  /**
   * 监控布局偏移
   */
  observeLayoutShift() {
    if ('LayoutShift' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            this.metrics.layoutShifts += entry.value;
          }
        }
      });
      
      observer.observe({ entryTypes: ['layout-shift'] });
    }
  }

  /**
   * 监控首次渲染
   */
  observeFirstRender() {
    // 监控 First Contentful Paint
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          this.metrics.renderTime = entry.startTime;
        }
      }
    });
    
    observer.observe({ entryTypes: ['paint'] });
  }

  /**
   * 获取字体加载性能报告
   */
  getPerformanceReport() {
    const report = {
      summary: {
        totalLoadTime: this.metrics.totalLoadTime,
        fontsLoaded: this.metrics.fontsLoaded,
        fontsError: this.metrics.fontsError,
        layoutShifts: this.metrics.layoutShifts,
        renderTime: this.metrics.renderTime
      },
      fontDetails: Array.from(this.loadTimes.entries()).map(([name, info]) => ({
        name,
        duration: info.duration,
        status: info.duration ? 'loaded' : 'loading'
      })),
      recommendations: this.generateRecommendations()
    };

    return report;
  }

  /**
   * 生成性能优化建议
   */
  generateRecommendations() {
    const recommendations = [];

    // 检查总加载时间
    if (this.metrics.totalLoadTime > 3000) {
      recommendations.push({
        type: 'warning',
        message: '字体加载时间过长，建议优化字体文件大小或使用字体子集'
      });
    }

    // 检查布局偏移
    if (this.metrics.layoutShifts > 0.1) {
      recommendations.push({
        type: 'warning',
        message: '检测到布局偏移，建议使用 font-display: swap 和合适的 fallback 字体'
      });
    }

    // 检查字体加载失败
    if (this.metrics.fontsError > 0) {
      recommendations.push({
        type: 'error',
        message: '部分字体加载失败，请检查字体文件路径和网络连接'
      });
    }

    // 检查渲染时间
    if (this.metrics.renderTime > 2500) {
      recommendations.push({
        type: 'info',
        message: '首次内容渲染时间较长，建议优化关键字体的加载优先级'
      });
    }

    // 如果没有问题，给出正面反馈
    if (recommendations.length === 0) {
      recommendations.push({
        type: 'success',
        message: '字体加载性能良好！'
      });
    }

    return recommendations;
  }

  /**
   * 输出性能指标到控制台
   */
  logPerformanceMetrics() {
    const report = this.getPerformanceReport();
    
    console.group('🎨 字体性能报告');
    console.log('📊 性能指标:', report.summary);
    console.log('📝 字体详情:', report.fontDetails);
    console.log('💡 优化建议:', report.recommendations);
    console.groupEnd();
  }

  /**
   * 导出性能数据
   */
  exportMetrics() {
    const report = this.getPerformanceReport();
    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `font-performance-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 获取 Web Vitals 相关的字体指标
   */
  getWebVitals() {
    return {
      CLS: this.metrics.layoutShifts, // Cumulative Layout Shift
      FCP: this.metrics.renderTime,   // First Contentful Paint
      fontLoadTime: this.metrics.totalLoadTime
    };
  }
}

// 创建全局性能监控实例
const fontPerformanceMonitor = new FontPerformanceMonitor();

// 导出监控器
export default fontPerformanceMonitor;

// 开发环境下提供全局访问
if (process.env.NODE_ENV === 'development') {
  window.fontPerformanceMonitor = fontPerformanceMonitor;
}
