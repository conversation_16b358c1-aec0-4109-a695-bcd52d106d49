/**
 * 字体加载管理器
 * 确保页面在字体加载期间不会出现空白，提供优雅的字体加载体验
 */

class FontLoader {
  constructor() {
    this.fonts = [
      // 主要字体 - 优先加载
      {
        family: 'Alibaba PuHuiTi 2.0',
        weight: '400',
        url: '/fonts/AlibabaPuHuiTi-2-55-Regular.woff2',
        priority: 'high'
      },
      {
        family: 'Alibaba PuHuiTi 2.0',
        weight: '300',
        url: '/fonts/AlibabaPuHuiTi-2-45-Light.woff2',
        priority: 'high'
      },
      {
        family: 'Alibaba PuHuiTi 2.0',
        weight: '500',
        url: '/fonts/AlibabaPuHuiTi-2-65-Medium.woff2',
        priority: 'high'
      },
      {
        family: 'Inter',
        weight: '400',
        url: '/fonts/Inter-VariableFont.woff2',
        priority: 'high'
      },
      {
        family: 'Inter',
        weight: '500',
        url: '/fonts/Inter-VariableFont.woff2',
        priority: 'high'
      },
      // 次要字体 - 延迟加载
      {
        family: 'Alibaba PuHuiTi 2.0',
        weight: '200',
        url: '/fonts/AlibabaPuHuiTi-2-35-Thin.woff2',
        priority: 'low'
      },
      {
        family: 'Alibaba PuHuiTi 2.0',
        weight: '700',
        url: '/fonts/AlibabaPuHuiTi-2-85-Bold.woff2',
        priority: 'low'
      },
      // Inter Variable Font 扩展字重
      {
        family: 'Inter',
        weight: '200',
        url: '/fonts/Inter-VariableFont.woff2',
        priority: 'low'
      },
      {
        family: 'Inter',
        weight: '700',
        url: '/fonts/Inter-VariableFont.woff2',
        priority: 'low'
      },
      {
        family: 'Inter',
        weight: '800',
        url: '/fonts/Inter-VariableFont.woff2',
        priority: 'low'
      },
      {
        family: 'Inter',
        weight: '900',
        url: '/fonts/Inter-VariableFont.woff2',
        priority: 'low'
      }
    ];
    
    this.loadedFonts = new Set();
    this.loadingPromises = new Map();
    this.callbacks = [];
    
    // 绑定方法
    this.init = this.init.bind(this);
    this.loadFont = this.loadFont.bind(this);
    this.loadHighPriorityFonts = this.loadHighPriorityFonts.bind(this);
    this.loadLowPriorityFonts = this.loadLowPriorityFonts.bind(this);
    this.onFontLoad = this.onFontLoad.bind(this);
    this.addLoadCallback = this.addLoadCallback.bind(this);
  }

  /**
   * 初始化字体加载器
   */
  async init() {
    try {
      // 检查浏览器是否支持 Font Loading API
      if ('fonts' in document) {
        await this.loadWithFontAPI();
      } else {
        // 降级到传统方法
        await this.loadWithFallback();
      }
    } catch (error) {
      console.warn('字体加载失败，使用系统字体:', error);
      this.handleFontLoadError();
    }
  }

  /**
   * 使用 Font Loading API 加载字体
   */
  async loadWithFontAPI() {
    // 先加载高优先级字体
    await this.loadHighPriorityFonts();
    
    // 标记主要字体已加载
    this.markFontsLoaded();
    
    // 延迟加载低优先级字体
    setTimeout(() => {
      this.loadLowPriorityFonts();
    }, 100);
  }

  /**
   * 降级方法：使用传统方式加载字体
   */
  async loadWithFallback() {
    const promises = this.fonts
      .filter(font => font.priority === 'high')
      .map(font => this.loadFontWithImage(font));
    
    await Promise.allSettled(promises);
    this.markFontsLoaded();
    
    // 延迟加载低优先级字体
    setTimeout(() => {
      const lowPriorityPromises = this.fonts
        .filter(font => font.priority === 'low')
        .map(font => this.loadFontWithImage(font));
      
      Promise.allSettled(lowPriorityPromises);
    }, 100);
  }

  /**
   * 加载高优先级字体
   */
  async loadHighPriorityFonts() {
    const highPriorityFonts = this.fonts.filter(font => font.priority === 'high');
    const promises = highPriorityFonts.map(font => this.loadFont(font));
    
    // 等待所有高优先级字体加载完成，或者超时
    await Promise.race([
      Promise.allSettled(promises),
      new Promise(resolve => setTimeout(resolve, 3000)) // 3秒超时
    ]);
  }

  /**
   * 加载低优先级字体
   */
  async loadLowPriorityFonts() {
    const lowPriorityFonts = this.fonts.filter(font => font.priority === 'low');
    const promises = lowPriorityFonts.map(font => this.loadFont(font));
    
    await Promise.allSettled(promises);
  }

  /**
   * 使用 Font Loading API 加载单个字体
   */
  async loadFont(fontConfig) {
    const { family, weight, url } = fontConfig;
    const fontKey = `${family}-${weight}`;
    
    if (this.loadedFonts.has(fontKey)) {
      return Promise.resolve();
    }
    
    if (this.loadingPromises.has(fontKey)) {
      return this.loadingPromises.get(fontKey);
    }

    const fontFace = new FontFace(family, `url(${url})`, {
      weight: weight,
      display: 'swap'
    });

    const promise = fontFace.load().then(() => {
      document.fonts.add(fontFace);
      this.loadedFonts.add(fontKey);
      this.onFontLoad(fontKey);
      return fontFace;
    }).catch(error => {
      console.warn(`字体加载失败: ${fontKey}`, error);
      throw error;
    });

    this.loadingPromises.set(fontKey, promise);
    return promise;
  }

  /**
   * 使用图片方式加载字体（降级方案）
   */
  loadFontWithImage(fontConfig) {
    return new Promise((resolve, reject) => {
      const { family, weight } = fontConfig;
      const fontKey = `${family}-${weight}`;
      
      if (this.loadedFonts.has(fontKey)) {
        resolve();
        return;
      }

      // 创建一个隐藏的测试元素
      const testElement = document.createElement('div');
      testElement.style.cssText = `
        position: absolute;
        left: -9999px;
        top: -9999px;
        font-size: 72px;
        font-family: ${family};
        font-weight: ${weight};
        visibility: hidden;
      `;
      testElement.textContent = '测试字体加载Test Font Loading';
      document.body.appendChild(testElement);

      // 检查字体是否加载
      const checkFont = () => {
        const computedStyle = window.getComputedStyle(testElement);
        const actualFamily = computedStyle.fontFamily;
        
        if (actualFamily.includes(family)) {
          this.loadedFonts.add(fontKey);
          this.onFontLoad(fontKey);
          document.body.removeChild(testElement);
          resolve();
        } else {
          setTimeout(checkFont, 100);
        }
      };

      // 开始检查
      setTimeout(checkFont, 100);
      
      // 超时处理
      setTimeout(() => {
        if (document.body.contains(testElement)) {
          document.body.removeChild(testElement);
          reject(new Error(`字体加载超时: ${fontKey}`));
        }
      }, 5000);
    });
  }

  /**
   * 字体加载完成回调
   */
  onFontLoad(fontKey) {
    console.log(`字体加载完成: ${fontKey}`);
    
    // 执行回调函数
    this.callbacks.forEach(callback => {
      try {
        callback(fontKey, this.loadedFonts);
      } catch (error) {
        console.error('字体加载回调执行失败:', error);
      }
    });
  }

  /**
   * 标记字体已加载，更新页面样式
   */
  markFontsLoaded() {
    // 添加字体已加载的类名
    document.documentElement.classList.add('fonts-loaded');
    document.body.classList.add('fonts-loaded');
    
    // 移除字体加载中的类名
    document.documentElement.classList.remove('font-loading');
    document.body.classList.remove('font-loading');
    
    // 触发自定义事件
    const event = new CustomEvent('fontsloaded', {
      detail: { loadedFonts: Array.from(this.loadedFonts) }
    });
    document.dispatchEvent(event);
  }

  /**
   * 处理字体加载错误
   */
  handleFontLoadError() {
    document.documentElement.classList.add('fonts-error');
    document.body.classList.add('fonts-error');
    
    const event = new CustomEvent('fontserror');
    document.dispatchEvent(event);
  }

  /**
   * 添加字体加载回调
   */
  addLoadCallback(callback) {
    if (typeof callback === 'function') {
      this.callbacks.push(callback);
    }
  }

  /**
   * 检查字体是否已加载
   */
  isFontLoaded(family, weight = '400') {
    return this.loadedFonts.has(`${family}-${weight}`);
  }

  /**
   * 获取已加载的字体列表
   */
  getLoadedFonts() {
    return Array.from(this.loadedFonts);
  }
}

// 创建全局字体加载器实例
const fontLoader = new FontLoader();

// 导出字体加载器
export default fontLoader;

// 自动初始化（如果在浏览器环境中）
if (typeof window !== 'undefined') {
  // DOM 加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fontLoader.init);
  } else {
    // DOM 已经加载完成
    fontLoader.init();
  }
}
