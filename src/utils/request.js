import axios from 'axios'

const service = axios.create({
//   baseURL: '/api', // 可根据需要配置
  timeout: 10000, // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    console.log("执行到拦截axios")
    // 可在此处添加 token 等全局 header
    config.headers = {
		/*配置请求数据格式Content-Type*/
		//传json数据格式给后端，如果后端是这个接收格式，配置如下（可不写，因为axios默认将 JavaScript 对象序列化为 json格式）
		'Content-Type':'application/json',
		//其他自定义的请求头也可写在这里，比如常见的请求头中携带tooken
	}	
     return config
  },
  error => Promise.reject(error)
)

// 响应拦截器
service.interceptors.response.use(
  response => response,
  error => {
    // 可统一处理错误提示
    return Promise.reject(error)
  }
)

export default service 