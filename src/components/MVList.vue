<template>
  <div class="title-section mt180">
    <h1 class="title">MV</h1>
  </div>
  <div class="mv-list">
    <div v-for="mv in mvList" :key="mv.id" class="mv-item">
      <div class="mv-thumbnail">
        <img :src="mv.thumbnail" :alt="mv.title" />
      </div>
      <div class="mv-info">
        <h3 class="mv-title">{{ mv.title }}</h3>
        <div class="mv-action">
          <a :href="mv.url" target="_blank">
            <img src="../assets/imgs/go.png" alt="go" />
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";
import { useRoute } from "vue-router";
const route = useRoute();

const props = defineProps({
  mvList: Array,
});
</script>

<style lang="scss" scoped>
@import "@/styles/title.scss";
.mv-list {
  padding-top: 60px;
  display: flex;
  flex-direction: column;
  gap: 30px;
  width: 100%;
  opacity: 0;
  animation: fadeInUp 0.8s ease-out 0.3s forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mv-item {
  display: flex;
  gap: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid #868686;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
  animation: mvItemSlideIn 0.6s ease-out forwards;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  &:hover {
    .play-overlay {
      opacity: 1;
    }

    .mv-thumbnail img {
      transform: scale(1.02);
    }
  }

  @include tablet-only {
    flex-direction: column;
    gap: 20px;
  }
}

.mv-thumbnail {
  position: relative;
  width: 720px;
  height: 405px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  @include tablet-only {
    width: 100%;
    height: 300px;
  }

  @media (max-width: 480px) {
    height: 200px;
  }
}

.mv-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
  gap: 30px;
  padding: 0;

  @include tablet-only {
    align-items: flex-start;
    gap: 20px;
  }
}

.mv-title {
  font-family: $font-family-chinese;
  font-weight: 700;
  font-size: 32px;
  line-height: 1.5em;
  color: $text-primary;
  margin: 0;
  // text-align: right;
  width: 100%;
  word-wrap: break-word;

  @include tablet-only {
    text-align: left;
    font-size: 28px;
  }

  @media (max-width: 480px) {
    font-size: 24px;
  }
}

.mv-action {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;

  img {
    width: 100%;
    height: 100%;
  }
  :hover {
    transform: scale(1.2);
  }
}

// 响应式设计
@media (max-width: 968px) {
  .mv-list {
    gap: 20px;
  }

  .mv-item {
    gap: 20px;
    padding-bottom: 20px;
  }

  .mv-thumbnail {
    width: 100%;
    height: 250px;
  }

  .mv-info {
    gap: 15px;
  }
}

@keyframes mvItemSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
