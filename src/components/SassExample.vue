<template>
  <div class="sass-example">
    <div class="container">
      <h2 class="example__title">Sass 样式示例</h2>
      
      <div class="example__grid">
        <div class="card card--primary">
          <h3 class="card__title">主色调卡片</h3>
          <p class="card__content">使用 $primary-color 变量</p>
        </div>
        
        <div class="card card--secondary">
          <h3 class="card__title">次要色调卡片</h3>
          <p class="card__content">使用 $text-secondary 变量</p>
        </div>
        
        <div class="card card--hover">
          <h3 class="card__title">悬停效果卡片</h3>
          <p class="card__content">使用 @include hover-lift 混入</p>
        </div>
      </div>
      
      <div class="example__buttons">
        <button class="btn btn--primary">主要按钮</button>
        <button class="btn btn--secondary">次要按钮</button>
        <button class="btn btn--outline">轮廓按钮</button>
      </div>
      
      <div class="example__responsive">
        <div class="responsive-text">
          <p>这段文字在不同屏幕尺寸下有不同的样式：</p>
          <ul>
            <li class="hide-mobile">桌面端可见</li>
            <li class="show-mobile">仅手机端可见</li>
            <li class="show-tablet">仅平板端可见</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Sass 样式示例组件
</script>

<style lang="scss" scoped>
.sass-example {
  padding: $spacing-3xl 0;
  background: $bg-primary;
  min-height: 100vh;
}

.example {
  &__title {
    @include font-primary($font-size-3xl, $font-weight-bold);
    @include text-center;
    color: $text-primary;
    margin-bottom: $spacing-2xl;
  }
  
  &__grid {
    @include grid-responsive(300px, $spacing-lg);
    margin-bottom: $spacing-2xl;
  }
  
  &__buttons {
    @include flex-center;
    gap: $spacing-lg;
    margin-bottom: $spacing-2xl;
    
    @include tablet-only {
      @include flex-column;
      gap: $spacing-md;
    }
  }
  
  &__responsive {
    @include flex-center;
  }
}

.card {
  background: $bg-white;
  padding: $spacing-xl;
  border-radius: $border-radius-lg;
  @include shadow-light;
  @include transition;
  
  &__title {
    @include font-primary($font-size-xl, $font-weight-bold);
    margin-bottom: $spacing-md;
  }
  
  &__content {
    @include font-secondary($font-size-base);
    color: $text-secondary;
    line-height: 1.6;
  }
  
  &--primary {
    border-left: 4px solid $primary-color;
    
    .card__title {
      color: $primary-color;
    }
  }
  
  &--secondary {
    border-left: 4px solid $text-secondary;
    
    .card__title {
      color: $text-secondary;
    }
  }
  
  &--hover {
    @include hover-lift(8px);
    cursor: pointer;
    
    &:hover {
      @include shadow-medium;
      
      .card__title {
        color: $primary-color;
      }
    }
  }
}

.btn {
  @include button-reset;
  @include font-primary($font-size-base, $font-weight-medium);
  padding: $spacing-md $spacing-xl;
  border-radius: $border-radius-md;
  @include transition;
  cursor: pointer;
  
  &--primary {
    background: $primary-color;
    color: $text-white;
    
    &:hover {
      background: $primary-dark;
      @include hover-lift(2px);
    }
  }
  
  &--secondary {
    background: $text-secondary;
    color: $text-white;
    
    &:hover {
      background: $primary-color;
      @include hover-scale(1.05);
    }
  }
  
  &--outline {
    background: transparent;
    color: $primary-color;
    border: 2px solid $primary-color;
    
    &:hover {
      background: $primary-color;
      color: $text-white;
    }
  }
}

.responsive-text {
  @include text-center;
  
  p {
    @include font-secondary($font-size-lg);
    margin-bottom: $spacing-md;
    
    @include tablet-only {
      font-size: $font-size-base;
    }
    
    @include mobile-only {
      font-size: $font-size-sm;
    }
  }
  
  ul {
    list-style: none;
    
    li {
      @include font-secondary($font-size-base);
      padding: $spacing-sm;
      margin: $spacing-xs 0;
      background: $bg-white;
      border-radius: $border-radius-sm;
      @include shadow-light;
    }
  }
}

// 响应式测试样式
@include mobile-only {
  .sass-example {
    padding: $spacing-lg 0;
  }
}

@include tablet-only {
  .example__grid {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }
}

@include desktop-up {
  .example__grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
</style>
