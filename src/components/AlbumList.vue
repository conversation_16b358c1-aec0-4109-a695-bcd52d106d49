<template>
  <div class="title-section mt180">
    <div class="title-with-pagination">
      <h1 class="title">{{ $t("artists.musicAlbum") }}</h1>
    </div>
    <!-- 分页按钮 -->
    <div class="page-section" v-if="totalPages > 1">
      <!-- 上一页 -->
      <div
        @click="preview"
        class="pre-button"
        :class="{ disabled: !canGoPrevious }"
        :style="{
          cursor: canGoPrevious ? 'pointer' : 'not-allowed',
          opacity: canGoPrevious ? 1 : 0.2,
        }"
      >
        <div class="pre-icon">
          <div class="arrow-tail-top"></div>
          <div class="arrow-tail-bottom"></div>
        </div>
      </div>
      <!-- 下一页 -->
      <div
        @click="next"
        class="next-button"
        :class="{ disabled: !canGoNext }"
        :style="{
          cursor: canGoNext ? 'pointer' : 'not-allowed',
          opacity: canGoNext ? 1 : 0.2,
        }"
      >
        <div class="next-icon">
          <div class="arrow-tail-top"></div>
          <div class="arrow-tail-bottom"></div>
        </div>
      </div>
    </div>
  </div>
  <div class="album-list">
    <div
      v-for="(album, index) in albums"
      :key="album.id"
      class="album-item"
      :class="{ expanded: expandedAlbum === album.id }"
      @click="toggleAlbum(album.id)"
    >
      <!-- 展开状态 - 显示完整信息 -->
      <div v-if="expandedAlbum === album.id" class="album-expanded">
        <div class="album-content">
          <div class="album-info">
            <div class="album-header">
              <h3 class="album-title">{{ album.title }}</h3>
              <div class="album-description">
                <p>{{ album.description }}</p>
              </div>
            </div>
            <div class="album-meta" @click="goToPage(album)">
              <div class="play-icon">
                <div class="play-button">
                  <div class="play-triangle"></div>
                  <div class="play-dot"></div>
                </div>
              </div>
              <span class="album-type">{{ album.type }}</span>
            </div>
          </div>
          <div class="album-cover">
            <img :src="album.cover" :alt="album.title" />
          </div>
        </div>
      </div>
      <!-- 折叠状态 - 只显示标题和箭头 -->
      <div v-else class="album-collapsed">
        <div class="album-title-collapsed">{{ album.title }}</div>
        <div class="album-cover-half">
          <div class="album-cover-mask"></div>
          <img :src="album.cover" :alt="album.title" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineProps, computed } from "vue";
import { useRouter } from "vue-router";
import { getAlbums } from "@/api/music";

const router = useRouter();
const props = defineProps({
  pid: String,
});
const allAlbums = ref([]); // 所有专辑数据
const albums = ref([]); // 当前页显示的专辑数据
// 当前展开的专辑ID
const expandedAlbum = ref(1); // 默认展开第一个专辑

// 分页相关
const currentPage = ref(1);
const pageSize = 2; // 每页5条数据
const totalPages = computed(() => Math.ceil(allAlbums.value.length / pageSize));

// 计算当前页的数据
const updateCurrentPageData = () => {
  const startIndex = (currentPage.value - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  albums.value = allAlbums.value.slice(startIndex, endIndex);

  // 如果当前页有数据，默认展开第一个
  if (albums.value.length > 0) {
    expandedAlbum.value = albums.value[0].id;
  } else {
    expandedAlbum.value = null;
  }

  console.log(
    `显示第${currentPage.value}页，共${totalPages.value}页，当前页数据:`,
    albums.value.length
  );
};

// 根据id获取专辑数据
async function fetchUserAlbums() {
  try {
    const res = await getAlbums({ id: props.pid });
    allAlbums.value = res.list || res;
    console.log("数据", allAlbums.value);
    console.log("总页数:", totalPages.value);
    console.log("当前页:", currentPage.value);
    // 获取数据后更新当前页数据
    updateCurrentPageData();
  } catch (e) {
    console.log("专辑错误", e);
  }
}

// 上一页
const preview = () => {
  if (!canGoPrevious.value) return; // 如果不能点击，直接返回
  currentPage.value--;
  updateCurrentPageData();
};

// 下一页
const next = () => {
  if (!canGoNext.value) return; // 如果不能点击，直接返回
  currentPage.value++;
  updateCurrentPageData();
};

// 判断是否可以点击上一页
const canGoPrevious = computed(() => currentPage.value > 1);

// 判断是否可以点击下一页
const canGoNext = computed(() => currentPage.value < totalPages.value);

// 切换专辑展开/折叠状态
const toggleAlbum = (albumId) => {
  expandedAlbum.value = expandedAlbum.value === albumId ? null : albumId;
};

const goToPage = (album) => {
  if (album.type === "Album") {
    router.push(`/albums/${album.id}`);
  } else if (album.type === "EP") {
    router.push(`/ep/${album.id}`);
  } else if (album.type === "Single") {
    router.push(`/single/${album.id}`);
  }
};

onMounted(() => {
  fetchUserAlbums();
});
</script>

<style lang="scss" scoped>
@import "@/styles/title.scss";

.album-list {
  width: 100%;
}

.album-item {
  border-bottom: 1px solid #ddd;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;

  &:last-child {
    border-bottom: none;
  }

  &.expanded {
    border-bottom: 1px solid #ddd;
  }
}

// 展开状态样式
.album-expanded {
  padding: 30px 30px 30px 0;
  animation: expandIn 0.5s ease-out;
}

.album-content {
  display: flex;
  gap: 60px;
  align-items: flex-start;

  @include tablet-only {
    flex-direction: column;
    gap: 30px;
  }
}

.album-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.album-header {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.album-title {
  font-family: $font-family-chinese;
  font-weight: 700;
  font-size: 48px;
  line-height: 1em;
  color: $text-primary;
  margin: 0;

  @include tablet-only {
    font-size: 36px;
  }

  @media (max-width: 480px) {
    font-size: 28px;
  }
}

.album-description {
  margin-top: 260px;
  width: 630px;
  height: 240px;
  overflow: hidden;
  text-overflow: ellipsis;
  p {
    font-family: $font-family-chinese;
    font-weight: 300;
    font-size: 20px;
    line-height: 2em;
    color: $text-light;
    margin: 0;
    white-space: pre-line;

    @include tablet-only {
      font-size: 18px;
      line-height: 1.8em;
    }
  }
}

.album-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.play-icon {
  width: 20px;
  height: 20px;
  position: relative;
}

.play-button {
  width: 100%;
  height: 100%;
  position: relative;
}

.play-triangle {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  background: $text-primary;
  border-radius: 50%;

  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    background: $bg-primary;
    border-radius: 50%;
    transform: translate(-50%, -50%);
  }
}

.play-dot {
  position: absolute;
  top: 9px;
  left: 9px;
  width: 2px;
  height: 2px;
  background: $text-primary;
  border-radius: 50%;
}

.album-type {
  font-family: $font-family-chinese;
  font-weight: 400;
  font-size: 20px;
  line-height: 1em;
  color: $text-primary;
}

.album-cover {
  width: 600px;
  height: 600px;
  flex-shrink: 0;
  border-radius: 300px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @include tablet-only {
    width: 400px;
    height: 400px;
    align-self: center;
  }

  @include mobile-only {
    width: 300px;
    height: 300px;
  }
}
.album-cover-half {
  width: 600px;
  height: 200px;
  overflow: hidden;
  position: relative;

  .album-cover-mask {
    width: 600px;
    height: 200px;
    position: absolute;
    right: 0;
    z-index: 99;
    background-image: url(../assets/imgs/mask-layer.png);
    background-size: 100%;
  }

  img {
    width: 100%;
  }
  @include tablet-only {
    width: 400px;
    height: 200px;
    align-self: center;

    .album-cover-mask {
      width: 400px;
      height: 200px;
    }
  }

  @include mobile-only {
    width: 300px;
    height: 100px;
    align-self: center;

    .album-cover-mask {
      width: 300px;
      height: 100px;
    }
  }
}

// 折叠状态样式
.album-collapsed {
  display: flex;
  justify-content: space-between;
  align-items: start;
  padding: 30px 30px 0 0;
  border-bottom: 1px solid #dddddd;
  transition: background-color 0.3s ease;

  @include tablet-only {
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: center;
    align-self: center;
  }

  @include mobile-only {
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: center;
    align-self: center;
  }
}

.album-title-collapsed {
  width: 630px;
  word-wrap: break-word;
  font-family: $font-family-chinese;
  font-weight: 400;
  font-size: 32px;
  line-height: 1.2em;
  color: $text-secondary;

  @include tablet-only {
    width: 400px;
    word-wrap: break-word;
    margin: 20px 0;
    font-size: 28px;
  }

  @media (max-width: 480px) {
    width: 300px;
    word-wrap: break-word;
    margin: 20px 0;
    font-size: 24px;
  }
}

// 响应式设计
@media (max-width: 968px) {
  .album-expanded {
    padding: 20px 20px 20px 0;
  }

  .album-collapsed {
    padding: 20px 20px 0 0;
  }

  .album-content {
    gap: 30px;
  }
}

// 动画关键帧
@keyframes expandIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
