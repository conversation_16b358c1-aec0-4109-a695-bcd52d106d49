<template>
  <nav class="navigation" :class="{ 'is-collapsed': isCollapsed }">
    <div class="nav-container">
      <!-- 汉堡菜单按钮 -->
      <button
        class="menu-button"
        @click="toggleFullScreenMenu"
        :aria-label="$t('nav.menu')"
      >
        <div class="menu-icon">
          <span class="menu-line"></span>
          <span class="menu-line"></span>
          <span class="menu-line"></span>
        </div>
      </button>

      <!-- Logo -->
      <div class="logo">
        <img src="@/assets/imgs/header-logo.png" alt="Logo" />
      </div>

      <!-- 语言切换器 -->
      <div class="language-switcher">
        <button
          class="language-button"
          @click="toggleLanguageMenu"
          :aria-label="currentLanguageLabel"
        >
          {{ currentLanguageCode }}
          <svg
            class="dropdown-icon"
            :class="{ 'is-open': isLanguageMenuOpen }"
            width="12"
            height="8"
            viewBox="0 0 12 8"
            fill="none"
          >
            <path
              d="M1 1L6 6L11 1"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>

        <!-- 语言下拉菜单 -->
        <div
          class="language-menu"
          :class="{ 'is-open': isLanguageMenuOpen }"
          @click.stop
        >
          <button
            v-for="lang in languages"
            :key="lang.code"
            class="language-option"
            :class="{ 'is-active': currentLocale === lang.code }"
            @click="changeLanguage(lang.code)"
          >
            <span class="language-code">{{ lang.code.toUpperCase() }}</span>
            <span class="language-name">{{ $t(`language.${lang.name}`) }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 全屏菜单 -->
    <FullScreenMenu
      :isOpen="isFullScreenMenuOpen"
      @close="closeFullScreenMenu"
    />
  </nav>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useLanguageStore } from "@/stores/language";
import FullScreenMenu from "./FullScreenMenu.vue";

// Navigation 组件 - 使用 script setup 语法糖
const languageStore = useLanguageStore();

const isCollapsed = ref(false);
const isFullScreenMenuOpen = ref(false);
const isLanguageMenuOpen = ref(false);

// 使用store中的语言数据
const languages = computed(() => languageStore.supportedLanguages);
const currentLocale = computed(() => languageStore.currentLanguage);
const currentLanguageCode = computed(() => languageStore.currentLanguageCode);
const currentLanguageLabel = computed(() => languageStore.currentLanguageLabel);
const isChangingLanguage = computed(() => languageStore.isChanging);

const toggleFullScreenMenu = () => {
  isFullScreenMenuOpen.value = !isFullScreenMenuOpen.value;
  if (isFullScreenMenuOpen.value) {
    document.body.style.overflow = "hidden";
  } else {
    document.body.style.overflow = "";
  }
};

const closeFullScreenMenu = () => {
  isFullScreenMenuOpen.value = false;
  document.body.style.overflow = "";
};

const toggleLanguageMenu = () => {
  isLanguageMenuOpen.value = !isLanguageMenuOpen.value;
};

const changeLanguage = async (langCode) => {
  await languageStore.changeLanguage(langCode);
  isLanguageMenuOpen.value = false;
};

const handleScroll = () => {
  isCollapsed.value = window.scrollY > 50;
};

const handleClickOutside = (event) => {
  // 关闭语言菜单
  if (!event.target.closest(".language-switcher")) {
    isLanguageMenuOpen.value = false;
  }

  // 关闭全屏菜单 - 如果点击的不是菜单按钮和全屏菜单区域
  if (
    isFullScreenMenuOpen.value &&
    !event.target.closest(".menu-button") &&
    !event.target.closest(".fullscreen-menu")
  ) {
    closeFullScreenMenu();
  }
};

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
  document.addEventListener("click", handleClickOutside);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
  document.removeEventListener("click", handleClickOutside);
  document.body.style.overflow = "";
});
</script>

<style lang="scss" scoped>
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: $z-index-fixed;
  background-color: $bg-primary;
  @include transition;

  &.is-collapsed {
    background-color: rgba(235, 235, 235, 0.95);
    backdrop-filter: blur(10px);
    color: $text-white;
  }
}

.nav-container {
  @include flex-between;
  padding: $spacing-xl $container-padding-desktop;
  margin: 0 auto;
  height: $nav-height-desktop;
  box-sizing: border-box;

  @include tablet-only {
    padding: $spacing-lg;
    height: $nav-height-mobile;
  }
}

// 汉堡菜单按钮
.menu-button {
  @include flex-center;
  @include button-reset;
  width: 24px;
  height: 24px;
}

.menu-icon {
  position: relative;
  width: 20px;
  height: 18px;
}

.menu-line {
  display: block;
  position: absolute;
  width: 20px;
  height: 3px;
  background-color: #000;
  border-radius: 1.5px;
  @include transition;

  &:nth-child(1) {
    top: 0;
  }

  &:nth-child(2) {
    top: 7.5px;
  }

  &:nth-child(3) {
    top: 15px;
  }
}

// Logo
.logo {
  flex: 1;
  display: flex;
  justify-content: center;

  @include tablet-up {
    flex: 0;
    justify-content: flex-start;
  }

  img {
    width: 128.95px;
    height: 30px;
    color: $text-white;

    @include tablet-only {
      width: 100px;
      height: 23px;
    }
  }
}

// 语言切换器
.language-switcher {
  position: relative;

  @include tablet-only {
    display: none;
  }
}

.language-button {
  @include button-reset;
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  color: $primary-color;
  @include font-secondary($font-size-xl, $font-weight-semibold);
  line-height: 1;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  @include transition;

  &:hover {
    background-color: rgba($text-white, 0.1);
  }
}

.dropdown-icon {
  @include transition(transform);

  &.is-open {
    transform: rotate(180deg);
  }
}

.language-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: $spacing-xs;
  background-color: $bg-white;
  // border-radius: $border-radius-lg;
  @include shadow-medium;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  @include transition;
  min-width: 200px;

  &.is-open {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.language-option {
  @include button-reset;
  @include flex-between;
  width: 100%;
  padding: $spacing-sm $spacing-md;
  text-align: left;
  @include transition(background-color, $transition-fast);

  &:hover {
    background-color: rgba($text-secondary, 0.1);
  }

  &.is-active {
    background-color: $primary-color;
    color: $text-white;
  }
}

.language-code {
  @include font-secondary($font-size-sm, $font-weight-semibold);
  color: $text-secondary;

  .language-option.is-active & {
    color: $text-white;
  }
}

.language-name {
  @include font-secondary($font-size-sm);
  color: $text-primary;

  .language-option.is-active & {
    color: $text-white;
  }
}
</style>
