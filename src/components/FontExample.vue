<template>
  <div class="font-example">
    <div class="container">
      <h1 class="example-title">字体系统示例</h1>

      <!-- 中文字体示例 -->
      <section class="font-section">
        <h2 class="section-title">中文字体 - Alibaba PuHuiTi 2.0</h2>
        <div class="font-samples">
          <div class="sample chinese-thin">
            <h3>Thin (200)</h3>
            <p>这是 Alibaba PuHuiTi 2.0 Thin 字体示例</p>
            <p>海之轨迹音乐工作室</p>
          </div>
          <div class="sample chinese-light">
            <h3>Light (300)</h3>
            <p>这是 Alibaba PuHuiTi 2.0 Light 字体示例</p>
            <p>海之轨迹音乐工作室</p>
          </div>
          <div class="sample chinese-regular">
            <h3>Regular (400)</h3>
            <p>这是 Alibaba PuHuiTi 2.0 Regular 字体示例</p>
            <p>海之轨迹音乐工作室</p>
          </div>
          <div class="sample chinese-medium">
            <h3>Medium (500)</h3>
            <p>这是 Alibaba PuHuiTi 2.0 Medium 字体示例</p>
            <p>海之轨迹音乐工作室</p>
          </div>
          <div class="sample chinese-semibold">
            <h3>SemiBold (600)</h3>
            <p>这是 Alibaba PuHuiTi 2.0 SemiBold 字体示例</p>
            <p>海之轨迹音乐工作室</p>
          </div>
          <div class="sample chinese-bold">
            <h3>Bold (700)</h3>
            <p>这是 Alibaba PuHuiTi 2.0 Bold 字体示例</p>
            <p>海之轨迹音乐工作室</p>
          </div>
        </div>
      </section>

      <!-- 英文字体示例 -->
      <section class="font-section">
        <h2 class="section-title">英文字体 - Inter Variable Font</h2>
        <div class="font-samples">
          <div class="sample english-extra-light">
            <h3>ExtraLight (200)</h3>
            <p>This is Inter ExtraLight font example</p>
            <p>Sea Track Music Studio</p>
          </div>
          <div class="sample english-light">
            <h3>Light (300)</h3>
            <p>This is Inter Light font example</p>
            <p>Sea Track Music Studio</p>
          </div>
          <div class="sample english-regular">
            <h3>Regular (400)</h3>
            <p>This is Inter Regular font example</p>
            <p>Sea Track Music Studio</p>
          </div>
          <div class="sample english-medium">
            <h3>Medium (500)</h3>
            <p>This is Inter Medium font example</p>
            <p>Sea Track Music Studio</p>
          </div>
          <div class="sample english-semibold">
            <h3>SemiBold (600)</h3>
            <p>This is Inter SemiBold font example</p>
            <p>Sea Track Music Studio</p>
          </div>
          <div class="sample english-bold">
            <h3>Bold (700)</h3>
            <p>This is Inter Bold font example</p>
            <p>Sea Track Music Studio</p>
          </div>
          <div class="sample english-extra-bold">
            <h3>ExtraBold (800)</h3>
            <p>This is Inter ExtraBold font example</p>
            <p>Sea Track Music Studio</p>
          </div>
          <div class="sample english-black">
            <h3>Black (900)</h3>
            <p>This is Inter Black font example</p>
            <p>Sea Track Music Studio</p>
          </div>
        </div>
      </section>

      <!-- 混合字体示例 -->
      <section class="font-section">
        <h2 class="section-title">混合字体示例</h2>
        <div class="mixed-samples">
          <div class="sample mixed-primary">
            <h3>主要字体（中文优先）</h3>
            <p>这是混合字体示例 Mixed Font Example 海之轨迹 Sea Track</p>
          </div>
          <div class="sample mixed-secondary">
            <h3>次要字体（英文优先）</h3>
            <p>
              This is mixed font example 这是混合字体示例 Sea Track 海之轨迹
            </p>
          </div>
        </div>
      </section>

      <!-- Inter Variable Font 特性展示 -->
      <section class="font-section">
        <h2 class="section-title">Inter Variable Font 特性展示</h2>
        <div class="inter-features">
          <div class="feature-card">
            <h3 class="feature-title">数字显示优化</h3>
            <p class="feature-numeric">1234567890</p>
            <p class="feature-description">等宽数字，适合数据展示</p>
          </div>
          <div class="feature-card">
            <h3 class="feature-title">大标题优化</h3>
            <p class="feature-display">Display Heading</p>
            <p class="feature-description">优化大字号显示效果</p>
          </div>
          <div class="feature-card">
            <h3 class="feature-title">按钮文字优化</h3>
            <button class="feature-button">Button Text</button>
            <p class="feature-description">优化按钮文字显示</p>
          </div>
          <div class="feature-card">
            <h3 class="feature-title">小字体优化</h3>
            <p class="feature-caption">Caption text for small sizes</p>
            <p class="feature-description">优化小字号可读性</p>
          </div>
        </div>
      </section>

      <!-- 应用场景示例 -->
      <section class="font-section">
        <h2 class="section-title">应用场景示例</h2>
        <div class="usage-examples">
          <div class="example-card">
            <h3 class="card-title">页面标题</h3>
            <p class="card-content">这是页面内容，使用默认的混合字体系统。</p>
            <button class="card-button">按钮文字</button>
          </div>
          <div class="example-card">
            <h3 class="card-title-en">Page Title</h3>
            <p class="card-content-en">
              This is page content using the mixed font system.
            </p>
            <button class="card-button-en">Button Text</button>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
// 字体示例组件
</script>

<style lang="scss" scoped>
.font-example {
  padding: $spacing-3xl 0;
  background: $bg-primary;
  min-height: 100vh;
}

.example-title {
  @include font-heading($font-size-4xl, $font-weight-light);
  @include text-center;
  color: $text-primary;
  margin-bottom: $spacing-3xl;
}

.font-section {
  margin-bottom: $spacing-3xl;
}

.section-title {
  @include font-chinese($font-size-2xl, $font-weight-medium);
  color: $text-primary;
  margin-bottom: $spacing-xl;
  border-bottom: 2px solid $primary-color;
  padding-bottom: $spacing-sm;
}

.font-samples {
  @include grid-responsive(300px, $spacing-lg);
  margin-bottom: $spacing-xl;
}

.mixed-samples {
  @include grid-responsive(400px, $spacing-lg);
  margin-bottom: $spacing-xl;
}

.sample {
  background: $bg-white;
  padding: $spacing-xl;
  border-radius: $border-radius-lg;
  @include shadow-light;

  h3 {
    @include font-chinese($font-size-lg, $font-weight-semibold);
    color: $text-secondary;
    margin-bottom: $spacing-md;
  }

  p {
    margin-bottom: $spacing-sm;
    font-size: $font-size-lg;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 中文字体样式
.chinese-thin p {
  @include font-chinese($font-size-lg, $font-weight-thin);
}

.chinese-light p {
  @include font-chinese($font-size-lg, $font-weight-light);
}

.chinese-regular p {
  @include font-chinese($font-size-lg, $font-weight-normal);
}

.chinese-medium p {
  @include font-chinese($font-size-lg, $font-weight-medium);
}

.chinese-semibold p {
  @include font-chinese($font-size-lg, $font-weight-semibold);
}

.chinese-bold p {
  @include font-chinese($font-size-lg, $font-weight-bold);
}

// 英文字体样式 - Inter Variable Font
.english-extra-light p {
  @include font-inter($font-size-lg, $font-weight-extra-light);
}

.english-light p {
  @include font-inter($font-size-lg, $font-weight-light);
}

.english-regular p {
  @include font-inter($font-size-lg, $font-weight-normal);
}

.english-medium p {
  @include font-inter($font-size-lg, $font-weight-medium);
}

.english-semibold p {
  @include font-inter($font-size-lg, $font-weight-semibold);
}

.english-bold p {
  @include font-inter($font-size-lg, $font-weight-bold);
}

.english-extra-bold p {
  @include font-inter($font-size-lg, $font-weight-extra-bold);
}

.english-black p {
  @include font-inter($font-size-lg, $font-weight-black);
}

// 混合字体样式
.mixed-primary p {
  @include font-primary($font-size-lg, $font-weight-normal);
}

.mixed-secondary p {
  @include font-secondary($font-size-lg, $font-weight-normal);
}

// 应用场景示例
.usage-examples {
  @include grid-responsive(400px, $spacing-lg);
}

.example-card {
  background: $bg-white;
  padding: $spacing-xl;
  border-radius: $border-radius-lg;
  @include shadow-light;
}

.card-title {
  @include font-heading($font-size-xl, $font-weight-medium);
  color: $text-primary;
  margin-bottom: $spacing-md;
}

.card-title-en {
  @include font-english($font-size-xl, $font-weight-normal);
  color: $text-primary;
  margin-bottom: $spacing-md;
}

.card-content {
  @include font-body($font-size-base, $font-weight-normal);
  color: $text-secondary;
  margin-bottom: $spacing-lg;
}

.card-content-en {
  @include font-secondary($font-size-base, $font-weight-normal);
  color: $text-secondary;
  margin-bottom: $spacing-lg;
}

.card-button {
  @include button-reset;
  @include font-button($font-size-base, $font-weight-medium);
  background: $primary-color;
  color: $text-white;
  padding: $spacing-sm $spacing-lg;
  border-radius: $border-radius-md;
  @include transition;

  &:hover {
    @include hover-lift(2px);
    opacity: 0.9;
  }
}

.card-button-en {
  @include button-reset;
  @include font-english($font-size-base, $font-weight-medium);
  background: $text-secondary;
  color: $text-white;
  padding: $spacing-sm $spacing-lg;
  border-radius: $border-radius-md;
  @include transition;

  &:hover {
    @include hover-lift(2px);
    opacity: 0.9;
  }
}

// Inter Variable Font 特性展示样式
.inter-features {
  @include grid-responsive(300px, $spacing-lg);
  margin-bottom: $spacing-xl;
}

.feature-card {
  background: $bg-white;
  padding: $spacing-xl;
  border-radius: $border-radius-lg;
  @include shadow-light;
  text-align: center;
}

.feature-title {
  @include font-chinese($font-size-lg, $font-weight-medium);
  color: $text-primary;
  margin-bottom: $spacing-md;
}

.feature-numeric {
  @include font-inter-numeric($font-size-2xl, $font-weight-medium);
  color: $primary-color;
  margin-bottom: $spacing-sm;
}

.feature-display {
  @include font-inter-display($font-size-2xl, $font-weight-medium);
  color: $text-primary;
  margin-bottom: $spacing-sm;
}

.feature-button {
  @include button-reset;
  @include font-inter-button($font-size-base, $font-weight-medium);
  background: $primary-color;
  color: $text-white;
  padding: $spacing-sm $spacing-lg;
  border-radius: $border-radius-md;
  margin-bottom: $spacing-sm;
  @include transition;

  &:hover {
    @include hover-lift(2px);
    opacity: 0.9;
  }
}

.feature-caption {
  @include font-inter-caption($font-size-sm, $font-weight-normal);
  color: $text-secondary;
  margin-bottom: $spacing-sm;
}

.feature-description {
  @include font-chinese($font-size-sm, $font-weight-normal);
  color: $text-secondary;
  opacity: 0.7;
  margin: 0;
}

// 响应式调整
@include tablet-only {
  .font-samples,
  .mixed-samples,
  .usage-examples,
  .inter-features {
    grid-template-columns: 1fr;
    gap: $spacing-md;
  }
}
</style>
