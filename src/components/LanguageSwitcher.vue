<template>
  <div class="language-switcher" :class="{ 'is-open': isMenuOpen }">
    <!-- 语言切换按钮 -->
    <button
      class="language-button"
      :class="{ 'is-loading': isChangingLanguage }"
      @click="toggleMenu"
      :aria-label="`${$t('common.language')}: ${currentLanguageLabel}`"
      :disabled="isChangingLanguage"
    >
      <span class="language-code">{{ currentLanguageCode }}</span>
      <span class="language-flag">{{ currentLanguageInfo.flag }}</span>
      <svg
        class="dropdown-icon"
        :class="{ 'is-open': isMenuOpen }"
        width="12"
        height="8"
        viewBox="0 0 12 8"
        fill="none"
      >
        <path
          d="M1 1L6 6L11 1"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>

    <!-- 语言下拉菜单 -->
    <div class="language-menu" :class="{ 'is-open': isMenuOpen }" @click.stop>
      <button
        v-for="lang in availableLanguages"
        :key="lang.code"
        class="language-option"
        :class="{ 'is-active': currentLanguage === lang.code }"
        @click="changeLanguage(lang.code)"
        :disabled="isChangingLanguage"
      >
        <span class="language-flag">{{ lang.flag }}</span>
        <span class="language-code">{{ lang.code.toUpperCase() }}</span>
        <span class="language-name">{{ $t(`language.${lang.name}`) }}</span>
      </button>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isChangingLanguage" class="loading-indicator">
      <div class="spinner"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { useLanguageStore } from "@/stores/language";

// LanguageSwitcher 组件 - 使用 script setup 语法糖
// 定义 props
const props = defineProps({
  variant: {
    type: String,
    default: "default", // 'default', 'compact', 'mobile'
    validator: (value) => ["default", "compact", "mobile"].includes(value),
  },
  placement: {
    type: String,
    default: "bottom-right", // 'bottom-left', 'bottom-right', 'top-left', 'top-right'
    validator: (value) =>
      ["bottom-left", "bottom-right", "top-left", "top-right"].includes(value),
  },
});

// 定义 emits
const emit = defineEmits(["language-changed"]);

const languageStore = useLanguageStore();
const isMenuOpen = ref(false);

// 计算属性
const currentLanguage = computed(() => languageStore.currentLanguage);
const currentLanguageInfo = computed(() => languageStore.currentLanguageInfo);
const currentLanguageCode = computed(() => languageStore.currentLanguageCode);
const currentLanguageLabel = computed(() => languageStore.currentLanguageLabel);
const availableLanguages = computed(() => languageStore.supportedLanguages);
const isChangingLanguage = computed(() => languageStore.isChanging);

// 方法
const toggleMenu = () => {
  if (isChangingLanguage.value) return;
  isMenuOpen.value = !isMenuOpen.value;
};

const closeMenu = () => {
  isMenuOpen.value = false;
};

const changeLanguage = async (langCode) => {
  if (isChangingLanguage.value) return;

  try {
    await languageStore.changeLanguage(langCode);
    emit("language-changed", {
      from: currentLanguage.value,
      to: langCode,
      languageInfo: languageStore.currentLanguageInfo,
    });
    closeMenu();
  } catch (error) {
    console.error("Failed to change language:", error);
  }
};

const handleClickOutside = (event) => {
  if (!event.target.closest(".language-switcher")) {
    closeMenu();
  }
};

const handleEscapeKey = (event) => {
  if (event.key === "Escape") {
    closeMenu();
  }
};

// 生命周期
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
  document.addEventListener("keydown", handleEscapeKey);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("keydown", handleEscapeKey);
});

// 在 script setup 中，所有的变量和函数都会自动暴露给模板
</script>

<style scoped>
.language-switcher {
  position: relative;
  display: inline-block;
}

.language-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0px;
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
  justify-content: space-between;
}

.language-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.language-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.language-button.is-loading {
  pointer-events: none;
}

.language-code {
  font-weight: 600;
  letter-spacing: 0.5px;
}

.language-flag {
  font-size: 16px;
}

.dropdown-icon {
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.dropdown-icon.is-open {
  transform: rotate(180deg);
}

.language-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 200px;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  overflow: hidden;
}

.language-menu.is-open {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.language-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: #333333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.language-option:hover {
  background: #f5f5f5;
}

.language-option:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.language-option.is-active {
  background: #e3f2fd;
  color: #1976d2;
  font-weight: 600;
}

.language-option .language-flag {
  font-size: 18px;
  flex-shrink: 0;
}

.language-option .language-code {
  font-weight: 600;
  letter-spacing: 0.5px;
  min-width: 24px;
}

.language-option .language-name {
  flex: 1;
}

.loading-indicator {
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  z-index: 10;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 变体样式 */
.language-switcher.compact .language-button {
  padding: 6px 8px;
  min-width: 60px;
  font-size: 12px;
}

.language-switcher.compact .language-menu {
  min-width: 160px;
}

.language-switcher.mobile .language-button {
  padding: 12px 16px;
  font-size: 16px;
  min-width: 100px;
}

.language-switcher.mobile .language-menu {
  min-width: 240px;
}

/* 位置变体 */
.language-switcher[data-placement="bottom-left"] .language-menu {
  left: 0;
  right: auto;
}

.language-switcher[data-placement="top-left"] .language-menu {
  bottom: calc(100% + 8px);
  top: auto;
  left: 0;
  right: auto;
}

.language-switcher[data-placement="top-right"] .language-menu {
  bottom: calc(100% + 8px);
  top: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .language-menu {
    min-width: 180px;
  }

  .language-option {
    padding: 14px 16px;
    font-size: 16px;
  }
}
</style>
