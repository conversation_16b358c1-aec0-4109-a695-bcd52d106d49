<template>
  <div v-if="showIndicator" class="font-loading-indicator">
    <div class="indicator-content">
      <div class="loading-spinner"></div>
      <p class="loading-text">正在加载字体...</p>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
      </div>
      <p class="progress-text">{{ loadedCount }}/{{ totalCount }} 字体已加载</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const showIndicator = ref(true)
const progress = ref(0)
const loadedCount = ref(0)
const totalCount = ref(6) // 总字体数量

// 字体加载事件处理
const handleFontLoad = (event) => {
  loadedCount.value++
  progress.value = (loadedCount.value / totalCount.value) * 100
  
  if (loadedCount.value >= totalCount.value) {
    // 所有字体加载完成，延迟隐藏指示器
    setTimeout(() => {
      showIndicator.value = false
    }, 500)
  }
}

const handleFontsLoaded = () => {
  showIndicator.value = false
}

const handleFontsError = () => {
  showIndicator.value = false
}

// 生命周期
onMounted(() => {
  // 监听字体加载事件
  document.addEventListener('fontsloaded', handleFontsLoaded)
  document.addEventListener('fontserror', handleFontsError)
  
  // 模拟字体加载进度（如果没有详细的加载事件）
  const timer = setInterval(() => {
    if (loadedCount.value < totalCount.value) {
      loadedCount.value++
      progress.value = (loadedCount.value / totalCount.value) * 100
    } else {
      clearInterval(timer)
      setTimeout(() => {
        showIndicator.value = false
      }, 500)
    }
  }, 300)
  
  // 最大显示时间限制
  setTimeout(() => {
    showIndicator.value = false
  }, 5000)
})

onUnmounted(() => {
  document.removeEventListener('fontsloaded', handleFontsLoaded)
  document.removeEventListener('fontserror', handleFontsError)
})
</script>

<style lang="scss" scoped>
.font-loading-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba($bg-primary, 0.95);
  backdrop-filter: blur(10px);
  z-index: $z-index-modal + 100;
  @include flex-center;
  
  // 淡入淡出动画
  animation: fadeIn 0.3s ease-out;
  
  &.fade-out {
    animation: fadeOut 0.3s ease-out forwards;
  }
}

.indicator-content {
  @include flex-column-center;
  gap: $spacing-lg;
  background: $bg-white;
  padding: $spacing-3xl;
  border-radius: $border-radius-xl;
  @include shadow-medium;
  max-width: 300px;
  width: 90%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba($primary-color, 0.3);
  border-top: 3px solid $primary-color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  @include font-chinese($font-size-lg, $font-weight-normal);
  color: $text-primary;
  margin: 0;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba($primary-color, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, $primary-color, $text-secondary);
  border-radius: 3px;
  transition: width 0.3s ease;
  animation: shimmer 2s infinite;
}

.progress-text {
  @include font-chinese($font-size-sm, $font-weight-normal);
  color: $text-secondary;
  margin: 0;
  text-align: center;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// 响应式设计
@include tablet-only {
  .indicator-content {
    padding: $spacing-xl;
    max-width: 280px;
  }
  
  .loading-text {
    font-size: $font-size-base;
  }
}

@include mobile-only {
  .indicator-content {
    padding: $spacing-lg;
    max-width: 260px;
  }
  
  .loading-spinner {
    width: 32px;
    height: 32px;
  }
  
  .loading-text {
    font-size: $font-size-sm;
  }
}
</style>
