# 搜索功能使用指南

## 功能概述

Artists页面已实现完整的搜索功能，支持按艺人名字或别名进行搜索，具有智能的显示模式切换和状态记忆功能。

## 核心特性

✅ **回车触发搜索** - 在搜索框中输入关键词，按回车键触发搜索  
✅ **智能模式切换** - 搜索时自动切换到列表模式展示结果  
✅ **状态记忆** - 记录搜索前的显示模式（单人/列表）  
✅ **清空恢复** - 清空搜索框时恢复所有数据和原始显示模式  
✅ **模糊匹配** - 支持按艺人名字和别名进行模糊搜索  

## 使用方法

### 1. 基本搜索
1. 在搜索框中输入艺人名字（如："魏晗"、"KIIRAS"）
2. 按回车键触发搜索
3. 页面自动切换到列表模式显示搜索结果

### 2. 清空搜索
1. 清空搜索框内容
2. 系统自动恢复所有艺人数据
3. 恢复搜索前的显示模式

### 3. 搜索状态
- **搜索中**: 显示模式强制为列表模式
- **搜索结果**: 以列表形式展示匹配的艺人
- **无结果**: 显示空列表
- **恢复状态**: 清空搜索后恢复原始模式和数据

## 技术实现

### 1. 搜索接口
使用`getPerson`接口进行搜索：
```javascript
// 搜索API调用
const res = await getPerson({ name: keyword });
```

### 2. Mock数据支持
Mock系统支持搜索参数：
```javascript
'/person': (params) => {
  let results = mockData.personList || [];
  if (params && params.name) {
    results = results.filter(person => 
      person.name.toLowerCase().includes(params.name.toLowerCase()) ||
      (person.alias && person.alias.toLowerCase().includes(params.name.toLowerCase()))
    );
  }
  return createMockResponse({
    list: results,
    total: results.length
  });
}
```

### 3. 状态管理
```javascript
// 搜索相关状态
const isSearching = ref(false);           // 是否正在搜索状态
const originalViewMode = ref("single");   // 记录搜索前的显示模式
const allArtists = ref([]);              // 保存所有艺人数据
```

### 4. 核心函数

#### 搜索处理
```javascript
const handleSearch = async () => {
  const keyword = searchQuery.value.trim();
  
  if (!keyword) {
    handleClearSearch();
    return;
  }
  
  // 记录搜索前的显示模式
  if (!isSearching.value) {
    originalViewMode.value = viewMode.value;
  }
  
  isSearching.value = true;
  viewMode.value = "list"; // 强制切换到列表模式
  
  const res = await getPerson({ name: keyword });
  artists.value = res.list || res || [];
};
```

#### 清空搜索
```javascript
const handleClearSearch = () => {
  isSearching.value = false;
  artists.value = allArtists.value;        // 恢复所有数据
  viewMode.value = originalViewMode.value; // 恢复原始模式
  
  // 如果是单人模式，恢复第一个艺人的选中状态
  if (viewMode.value === "single" && artists.value.length > 0) {
    selectedArtist.value = artists.value[0];
  }
};
```

## 用户体验

### 搜索流程
1. **初始状态**: 用户在单人模式或列表模式浏览艺人
2. **开始搜索**: 输入关键词，按回车
3. **搜索结果**: 自动切换到列表模式显示结果
4. **清空搜索**: 删除搜索内容，恢复原始状态

### 智能特性
- **自动模式切换**: 搜索时强制使用列表模式，便于查看多个结果
- **状态记忆**: 记住用户搜索前的偏好模式
- **即时清空**: 删除搜索内容时立即恢复，无需额外操作
- **模糊匹配**: 支持名字和别名的模糊搜索

## 测试用例

### 可搜索的艺人数据
- **魏晗** (别名: KIIRAS)
- **KIIRAS** (别名: 魏晗)
- **haha** (别名: 魏晗1)

### 测试场景
1. **搜索"魏晗"** - 应返回包含"魏晗"的艺人
2. **搜索"KIIRAS"** - 应返回别名包含"KIIRAS"的艺人
3. **搜索"不存在"** - 应返回空结果
4. **清空搜索** - 应恢复所有数据和原始模式

## 开发说明

### 添加新的搜索字段
如需支持更多搜索字段，修改mock处理逻辑：
```javascript
results = results.filter(person => 
  person.name.toLowerCase().includes(keyword) ||
  person.alias.toLowerCase().includes(keyword) ||
  person.category.toLowerCase().includes(keyword) // 新增字段
);
```

### 扩展搜索功能
- 可以添加高级搜索选项
- 支持多关键词搜索
- 添加搜索历史记录
- 实现搜索建议功能

这个搜索功能提供了完整的用户体验，包括智能的模式切换和状态记忆，让用户可以无缝地在搜索和浏览之间切换。
