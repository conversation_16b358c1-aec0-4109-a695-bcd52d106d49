# 字体系统使用指南

## 快速开始

### 1. 字体文件概览
项目现在包含完整的 Alibaba PuHuiTi 2.0 字重系列：

```
public/fonts/
├── AlibabaPuHuiTi-2-35-Thin.woff2      # Thin (200)
├── AlibabaPuHuiTi-2-45-Light.woff2     # Light (300)
├── AlibabaPuHuiTi-2-55-Regular.woff2   # Regular (400)
├── AlibabaPuHuiTi-2-65-Medium.woff2    # Medium (500)
├── AlibabaPuHuiTi-2-85-Bold.woff2      # Bold (700)
└── Inter-VariableFont.woff2            # Inter Variable Font (100-900)
```

### 2. 基础使用

#### 在 Vue 组件中使用字体
```vue
<template>
  <div class="content">
    <h1 class="title">页面标题</h1>
    <p class="text">正文内容</p>
    <button class="btn">按钮</button>
  </div>
</template>

<style lang="scss" scoped>
.title {
  @include font-safe-chinese($font-size-2xl, $font-weight-medium);
}

.text {
  @include font-safe-primary($font-size-base, $font-weight-normal);
}

.btn {
  @include font-safe-secondary($font-size-base, $font-weight-medium);
}
</style>
```

## 字体混入系统

### 1. 基础字体混入
```scss
// 纯中文字体
@include font-chinese($size, $weight, $line-height);

// 纯英文字体
@include font-english($size, $weight, $line-height);

// 主要字体（中文优先）
@include font-primary($size, $weight, $line-height);

// 次要字体（英文优先）
@include font-secondary($size, $weight, $line-height);

// 默认字体
@include font-default($size, $weight, $line-height);
```

### 2. 安全字体混入（推荐）
```scss
// 带 fallback 保护的字体混入
@include font-safe-chinese($size, $weight, $line-height);
@include font-safe-english($size, $weight, $line-height);
@include font-safe-primary($size, $weight, $line-height);
@include font-safe-secondary($size, $weight, $line-height);
```

### 3. 特殊用途混入
```scss
// 标题字体
@include font-heading($size, $weight);

// 正文字体
@include font-body($size, $weight);

// 按钮字体
@include font-button($size, $weight);

// 代码字体
@include font-code($size);
```

### 4. 响应式字体混入
```scss
// 响应式标题
@include font-responsive-heading($desktop-size, $tablet-size, $mobile-size);

// 响应式正文
@include font-responsive-body($desktop-size, $tablet-size, $mobile-size);
```

## 字重变量

### 可用字重
```scss
$font-weight-thin: 200;       // Thin - 超细
$font-weight-light: 300;      // Light - 细
$font-weight-normal: 400;     // Regular - 常规
$font-weight-medium: 500;     // Medium - 中等
$font-weight-semibold: 600;   // SemiBold - 半粗
$font-weight-bold: 700;       // Bold - 粗体
```

### 使用示例
```scss
.ultra-light-title {
  @include font-safe-chinese($font-size-4xl, $font-weight-thin);
}

.light-subtitle {
  @include font-safe-chinese($font-size-2xl, $font-weight-light);
}

.normal-text {
  @include font-safe-primary($font-size-base, $font-weight-normal);
}

.medium-emphasis {
  @include font-safe-chinese($font-size-lg, $font-weight-medium);
}

.bold-heading {
  @include font-safe-chinese($font-size-xl, $font-weight-bold);
}
```

## 字体加载优化

### 1. 自动加载管理
字体加载器会自动：
- 优先加载常用字重（Regular, Light, Medium）
- 延迟加载特殊字重（Thin, Bold）
- 提供加载失败的 fallback
- 监控加载性能

### 2. 加载状态监听
```javascript
// 监听字体加载完成
document.addEventListener('fontsloaded', (event) => {
  console.log('字体加载完成', event.detail);
});

// 监听字体加载失败
document.addEventListener('fontserror', () => {
  console.warn('字体加载失败，使用系统字体');
});
```

### 3. 性能监控
```javascript
// 开发环境下查看性能报告
if (process.env.NODE_ENV === 'development') {
  // 获取性能报告
  const report = window.fontPerformanceMonitor.getPerformanceReport();
  console.log(report);
  
  // 导出性能数据
  window.fontPerformanceMonitor.exportMetrics();
}
```

## 最佳实践

### 1. 字体选择原则
- **标题**: 使用 Thin 或 Light 字重，营造现代感
- **正文**: 使用 Regular 字重，确保可读性
- **强调**: 使用 Medium 或 Bold 字重，突出重点
- **按钮**: 使用 Medium 字重，平衡美观和可读性

### 2. 语言适配
```scss
// 纯中文内容
.chinese-content {
  @include font-safe-chinese($font-size-base, $font-weight-normal);
}

// 纯英文内容
.english-content {
  @include font-safe-english($font-size-base, $font-weight-normal);
}

// 中英文混合（中文优先）
.mixed-content-cn {
  @include font-safe-primary($font-size-base, $font-weight-normal);
}

// 中英文混合（英文优先）
.mixed-content-en {
  @include font-safe-secondary($font-size-base, $font-weight-normal);
}
```

### 3. 响应式设计
```scss
.responsive-title {
  @include font-responsive-heading(
    $font-size-4xl,  // 桌面端
    $font-size-3xl,  // 平板端
    $font-size-2xl   // 移动端
  );
  font-weight: $font-weight-light;
}
```

## 常见使用场景

### 1. 页面标题
```scss
.page-title {
  @include font-safe-chinese($font-size-4xl, $font-weight-thin);
  color: $text-primary;
  text-align: center;
  margin-bottom: $spacing-3xl;
}
```

### 2. 卡片标题
```scss
.card-title {
  @include font-safe-chinese($font-size-xl, $font-weight-medium);
  color: $text-primary;
  margin-bottom: $spacing-md;
}
```

### 3. 正文段落
```scss
.paragraph {
  @include font-safe-primary($font-size-base, $font-weight-normal);
  color: $text-secondary;
  line-height: 1.6;
  margin-bottom: $spacing-lg;
}
```

### 4. 导航菜单
```scss
.nav-item {
  @include font-safe-secondary($font-size-lg, $font-weight-normal);
  color: $text-primary;
  
  &:hover {
    font-weight: $font-weight-medium;
  }
}
```

### 5. 按钮文字
```scss
.button {
  @include font-safe-secondary($font-size-base, $font-weight-medium);
  background: $primary-color;
  color: $text-white;
  padding: $spacing-sm $spacing-lg;
  border-radius: $border-radius-md;
}
```

## 调试和测试

### 1. 字体测试页面
访问 `/font-test` 路由查看所有字体效果：
```
http://localhost:3000/font-test
```

### 2. 浏览器开发者工具
- 检查 Network 面板确认字体文件加载
- 使用 Elements 面板查看实际应用的字体
- 在 Console 中查看字体加载日志

### 3. 性能检查
```javascript
// 检查字体是否加载完成
document.fonts.ready.then(() => {
  console.log('所有字体加载完成');
});

// 检查特定字体
document.fonts.check('16px "Alibaba PuHuiTi 2.0"');
```

## 故障排除

### 1. 字体未显示
- 检查字体文件路径是否正确
- 确认浏览器支持 WOFF2 格式
- 查看 Console 是否有加载错误

### 2. 字体加载缓慢
- 检查网络连接
- 考虑使用 CDN 加速
- 优化字体文件大小

### 3. 布局偏移
- 确保使用了 `font-display: swap`
- 检查 fallback 字体是否合适
- 使用安全字体混入

## 扩展和自定义

### 1. 添加新字体
1. 将字体文件放入 `public/fonts/`
2. 在 `src/styles/fonts.scss` 中添加 @font-face 定义
3. 更新字体变量和混入
4. 添加到字体加载器配置

### 2. 自定义字体混入
```scss
// 创建自定义字体混入
@mixin font-custom-brand($size: $font-size-base) {
  @include font-safe-chinese($size, $font-weight-light);
  letter-spacing: 0.05em;
  text-transform: uppercase;
}
```

### 3. 性能优化
- 使用字体子集化减小文件大小
- 实现字体的懒加载
- 配置 CDN 加速字体加载

## 总结

通过这套字体系统，您可以：
- 🎨 使用完整的 Alibaba PuHuiTi 2.0 字重系列
- ⚡ 享受优化的字体加载性能
- 🛡️ 确保页面在字体加载期间不会空白
- 📊 监控字体加载性能
- 🔧 使用简单易用的 Sass 混入系统

记住始终使用 `font-safe-*` 混入来确保最佳的用户体验！
