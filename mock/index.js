import Mock from 'mockjs'

// 生成完整的新闻数据数组
const total = 23
const allNews = Mock.mock({
  [`list|${total}`]: [
    {
      'id|+1': 1,
      title: '@ctitle(10, 18)',
      date: '@date("yyyy-MM-dd")',
      author: '@cname',
      tag: '@pick(["新闻", "活动", "公告"])',
      cover: 'https://picsum.photos/seed/@id/600/300',
      content: '@cparagraph(3, 8)'
    }
  ]
}).list

// 新闻列表 mock，支持分页
console.log("执行到拦截",Mock.mock('/api/news'))
Mock.mock(RegExp('/api/news' + ".*"), 'get', (options) => {
  const url = new URL('http://dummy.com' + options.url)
 
  const page = Number(url.searchParams.get('page')) || 1
  const pageSize = Number(url.searchParams.get('pageSize')) || 5
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const list = allNews.slice(start, end)
  return {
    code: 0,
    data: {
      list,
      total
    }
  }
})

// banner mock
Mock.mock('/api/banner', 'get', {
  code: 0,
  data: [
    { id: 1, img: 'https://picsum.photos/seed/banner1/1200/400', link: '#' },
    { id: 2, img: 'https://picsum.photos/seed/banner2/1200/400', link: '#' }
  ]
}) 

//艺人，词曲人，制作人
Mock.mock(RegExp('/api/personList' + ".*"), 'get',{
    code: 0,
    data:[{
    id: "weihan",
    name: "魏晗",
    alias: "KIIRAS",
    category: "合作艺人",
    image: artistPlaceholder1,
  },
  {
    id: "kiiras",
    name: "KIIRAS",
    alias: "魏晗",
    category: "合作艺人",
    image: artistPlaceholder2,
  },
  {
    id: "kii",
    name: "haha",
    alias: "魏晗1",
    category: "Alibaba PuHuiTi 2.0",
    image: artistPlaceholder3,
  },
  {
    id: "weihan1",
    name: "魏晗",
    alias: "KIIRAS",
    category: "合作艺人",
    image: artistPlaceholder1,
  },
  {
    id: "kiiras2",
    name: "KIIRAS",
    alias: "魏晗",
    category: "合作艺人",
    image: artistPlaceholder2,
  },
  {
    id: "kii3",
    name: "haha",
    alias: "魏晗1",
    category: "合作艺人",
    image: artistPlaceholder3,
  },
  {
    id: "weihan4",
    name: "魏晗",
    alias: "KIIRAS",
    category: "自由",
    image: artistPlaceholder1,
  },
  {
    id: "kiiras5",
    name: "KIIRAS",
    alias: "魏晗",
    category: "[合作艺人]",
    image: artistPlaceholder2,
  },
  {
    id: "kii6",
    name: "haha",
    alias: "魏晗1",
    category: "[合作艺人]",
    image: artistPlaceholder3,
  },]
})

// 专辑EP数据 mock
Mock.mock(RegExp('/api/albums' + ".*"), 'get', {
  code: 0,
  data: [
    {
      id: 1,
      title: 'KILL MA BO$$',
      artist: 'KIIRAS',
      cover: artistPlaceholder1,
      releaseDate: '2024-01-15',
      songCount: 8,
      duration: '32:45',
      type: 'EP'
    },
    {
      id: 2,
      title: '告别秀',
      artist: 'KIIRAS',
      cover: artistPlaceholder2,
      releaseDate: '2023-12-20',
      songCount: 6,
      duration: '24:30',
      type: 'EP'
    },
    {
      id: 3,
      title: '不完美主义',
      artist: 'KIIRAS',
      cover: artistPlaceholder3,
      releaseDate: '2023-11-10',
      songCount: 10,
      duration: '38:20',
      type: 'Album'
    },
    {
      id: 4,
      title: '《KILL MA BO$$》',
      artist: 'KIIRAS',
      cover: artistPlaceholder1,
      releaseDate: '2023-10-05',
      songCount: 7,
      duration: '28:15',
      type: 'EP'
    },
    {
      id: 5,
      title: 'KILL MA BO$$',
      artist: 'KIIRAS',
      cover: artistPlaceholder2,
      releaseDate: '2023-09-18',
      songCount: 9,
      duration: '35:40',
      type: 'Album'
    },
    {
      id: 6,
      title: 'KILL MA BO$$',
      artist: 'KIIRAS',
      cover: artistPlaceholder3,
      releaseDate: '2023-08-25',
      songCount: 5,
      duration: '22:10',
      type: 'EP'
    }
  ]
})
