# API配置使用指南

## 概述

本项目已配置了axios用于HTTP请求，支持开发环境自动使用mock数据，生产环境使用真实接口地址。**无需在每个API函数中写if判断**，系统会自动处理mock和真实API的切换。

## 核心特性

✅ **自动Mock切换** - 开发环境自动使用mock数据，无需手动判断  
✅ **统一API接口** - 所有API函数写法一致，不区分mock和真实环境  
✅ **智能路径匹配** - 支持动态路径的mock数据匹配  
✅ **统一错误处理** - 网络错误、业务错误统一处理  
✅ **请求响应日志** - 自动记录请求和响应信息  

## 文件结构

```
src/
├── api/
│   ├── request.js      # axios配置、拦截器和mock处理
│   ├── home.js         # 首页相关API接口
│   ├── music.js        # 音乐相关API接口
│   └── menu.js         # 菜单相关API (已存在)
├── mock/
│   └── index.js        # mock数据配置
└── views/
    ├── ApiTest.vue     # API测试页面
    └── SimpleTest.vue  # 简单测试页面
```

## 核心架构

### 1. 统一的API写法

所有API函数都使用相同的写法，无需区分环境：

```javascript
// src/api/home.js
export async function getPersonList(params = {}) {
  return request.get('/personList', { params })
}

export async function getNewsList(params = {}) {
  return request.get('/news', { params })
}
```

### 2. 自动Mock处理

在`src/api/request.js`中配置mock映射表：

```javascript
const mockApiMap = {
  '/personList': () => createMockResponse({
    list: mockData.personList,
    total: mockData.personList.length
  }),
  '/news': () => createMockResponse({
    list: mockData.news,
    total: mockData.news.length
  }),
  '/banner': () => createMockResponse(mockData.banner)
}
```

### 3. 智能拦截器

请求拦截器会自动检测：
- 如果是开发环境且有对应的mock数据，直接返回mock响应
- 如果是生产环境或没有mock数据，发送真实HTTP请求

## 使用方法

### 1. 添加新的API接口

只需要在对应的API文件中添加函数：

```javascript
// src/api/music.js
export const songsAPI = {
  async getSongs(params = {}) {
    return request.get('/songs', { params })
  },
  
  async getSongDetail(id) {
    return request.get(`/songs/${id}`)
  }
}
```

### 2. 添加对应的Mock数据

在`src/mock/index.js`中添加数据：

```javascript
export const mockData = {
  songs: [
    { id: 1, title: '歌曲1', artist: '艺人1' },
    { id: 2, title: '歌曲2', artist: '艺人2' }
  ]
}
```

在`src/api/request.js`中添加映射：

```javascript
const mockApiMap = {
  '/songs': () => createMockResponse(mockData.songs)
}
```

### 3. 在组件中使用

```javascript
import { getPersonList } from '@/api/home'

export default {
  async mounted() {
    try {
      const result = await getPersonList({ type: 'artist' })
      console.log('艺人数据:', result)
    } catch (error) {
      console.error('获取失败:', error)
    }
  }
}
```

## 环境配置

### 开发环境 (.env.development)
```
NODE_ENV=development
VITE_USE_MOCK=true
VITE_API_BASE_URL=http://localhost:3001
```

### 生产环境 (.env.production)
```
NODE_ENV=production
VITE_USE_MOCK=false
VITE_API_BASE_URL=https://your-api-domain.com
```

## 测试页面

- **简单测试**: `http://localhost:3001/simple-test`
- **完整测试**: `http://localhost:3001/api-test`
- **Artists页面**: `http://localhost:3001/artists`

## 开发命令

```bash
# 开发环境启动（自动使用mock）
npm run dev

# 生产环境构建
npm run build

# 预览构建结果
npm run preview
```

## 优势

1. **代码简洁** - 无需在每个API函数中写if判断
2. **维护方便** - mock逻辑集中管理
3. **类型安全** - 统一的API接口定义
4. **开发效率** - 前端开发不依赖后端接口
5. **部署灵活** - 环境切换只需修改配置文件
